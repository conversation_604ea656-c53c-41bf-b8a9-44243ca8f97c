//+------------------------------------------------------------------+
//|                        London_Breakout_Pro_v1.mq5              |
//|                     Copyright 2025, Professional Trading       |
//|              Comprehensive London Breakout Strategy             |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Professional Trading"
#property link      "https://www.protrading.com"
#property version   "1.00"

// Using standard MQL5 trading functions instead of CTrade class

//--- Asian Range Detection
input group "=== Asian Range Settings ===="
input int             InpAsianStartHour = 23;              // Asian session start (GMT)
input int             InpAsianEndHour = 6;                 // Asian session end (GMT)
input int             InpMinRangePips = 5;                 // Minimum range size (pips)
input int             InpMaxRangePips = 150;               // Maximum range size (pips)
input double          InpRangeValidityPercent = 50.0;      // % of Asian session for valid range

//--- London Breakout Settings
input group "=== London Breakout Settings ===="
input int             InpLondonStartHour = 7;              // London session start (GMT)
input int             InpLondonEndHour = 10;               // London session end (GMT) 
input double          InpBreakoutBuffer = 2.0;             // Breakout buffer (pips)
input bool            InpRequireVolumeConfirmation = false; // Require volume spike
input double          InpVolumeMultiplier = 1.2;           // Volume vs average multiplier
input int             InpVolumeLookback = 20;              // Volume average lookback
input bool            InpAllowBothDirections = true;       // Allow long and short breakouts

//--- Advanced Risk Management
input group "=== Advanced Risk Management ===="
input double          InpRiskPercentage = 1.0;             // Risk per trade (%)
input double          InpMaxDailyLoss = 3.0;               // Max daily loss (%)
input double          InpMaxWeeklyLoss = 8.0;              // Max weekly loss (%)
input int             InpMaxConcurrentTrades = 2;          // Max concurrent trades
input double          InpMaxDrawdownPercent = 15.0;        // Max drawdown before pause
input int             InpMaxConsecutiveLosses = 4;         // Max consecutive losses
input double          InpCorrelationLimit = 0.7;           // Max correlation between pairs
input bool            InpUseAdaptiveRisk = true;           // Adaptive risk scaling
input double          InpMaxPositionSize = 8.0;            // Max position size (% of account)
input double          InpEquityProtection = 95.0;          // Stop trading below % of peak equity

//--- Dynamic Exit Strategy
input group "=== Dynamic Exit Strategy ===="
input double          InpMinRiskReward = 1.5;              // Minimum R:R ratio
input double          InpBaseRiskReward = 2.0;             // Base R:R target
input double          InpMaxRiskReward = 4.0;              // Maximum R:R target
input bool            InpUseDynamicTP = true;              // Use dynamic take profit
input bool            InpUseBreakeven = true;              // Enable breakeven
input double          InpBreakevenRR = 0.8;                // R:R to move to breakeven
input double          InpBreakevenBuffer = 0.2;            // Breakeven buffer R:R
input bool            InpUsePartialClose = true;           // Enable partial profit taking
input double          InpPartialClose1_RR = 1.0;           // First partial close R:R
input double          InpPartialClose1_Percent = 30.0;     // First partial close %
input double          InpPartialClose2_RR = 2.0;           // Second partial close R:R
input double          InpPartialClose2_Percent = 40.0;     // Second partial close %

//--- Smart Trailing Stop
input group "=== Smart Trailing Stop ===="
input bool            InpUseTrailingStop = true;           // Enable trailing stop
input double          InpTrailStartRR = 1.2;               // R:R to start trailing
input ENUM_TIMEFRAMES InpTrailTimeframe = PERIOD_M15;      // Timeframe for trailing
input int             InpTrailATRPeriod = 14;              // ATR period for trailing
input double          InpTrailATRMultiplier = 2.0;         // ATR multiplier for distance
input bool            InpUseSwingTrailing = true;          // Use swing high/low trailing
input int             InpSwingLookback = 10;               // Swing lookback period

//--- Time Management
input group "=== Time Management ===="
input bool            InpUseTimeFilters = true;            // Enable time filters
input bool            InpAvoidNews = true;                 // Avoid high impact news
input int             InpNewsBufferMinutes = 30;           // News avoidance buffer
input bool            InpCloseBeforeWeekend = true;        // Close positions before weekend
input int             InpFridayCloseHour = 20;             // Friday close hour (GMT)
input int             InpMaxTradeHours = 6;                // Max hours to keep trade open
input int             InpCooldownMinutes = 60;             // Cooldown between trades

//--- Market Condition Filters
input group "=== Market Condition Filters ===="
input bool            InpUseVolatilityFilter = false;      // Use volatility filter
input int             InpATRPeriod = 14;                   // ATR period for volatility
input double          InpMinVolatilityRatio = 0.5;         // Min ATR vs average ratio
input double          InpMaxVolatilityRatio = 5.0;         // Max ATR vs average ratio
input int             InpATRAveragePeriod = 50;             // ATR average period
input bool            InpUseSpreadFilter = false;          // Use spread filter
input double          InpMaxSpreadPips = 5.0;              // Max allowed spread
input bool            InpUseLiquidityFilter = false;       // Use liquidity filter
input double          InpMinLiquidityScore = 0.6;          // Min liquidity score (0-1)

//--- Multi-Pair Optimization
input group "=== Multi-Pair Settings ===="
input bool            InpOptimizeForEURUSD = true;         // Optimize for EURUSD
input bool            InpOptimizeForGBPUSD = true;         // Optimize for GBPUSD
input bool            InpOptimizeForUSDJPY = true;         // Optimize for USDJPY
input bool            InpOptimizeForAUDUSD = true;         // Optimize for AUDUSD
input double          InpPairSpecificMultiplier = 1.0;     // Pair-specific adjustment

//--- Notifications & Monitoring
input group "=== Notifications & Monitoring ===="
input bool            InpEnableAlerts = true;              // Enable alerts
input bool            InpEnablePushNotifications = false;  // Enable push notifications
input bool            InpEnableEmailNotifications = false; // Enable email notifications
input bool            InpNotifyOnSetup = true;             // Notify on range setup
input bool            InpNotifyOnBreakout = true;          // Notify on breakout
input bool            InpNotifyOnClose = true;             // Notify on trade close
input bool            InpNotifyOnRisk = true;              // Notify on risk events
input bool            InpDetailedLogging = true;           // Enable detailed logging

//--- General Settings
input group "=== General Settings ===="
input ulong           InpMagicNumber = 888999;             // Magic number
input string          InpTradeComment = "LondonBO_Pro";     // Trade comment

//--- Global Variables
ulong MagicNumber = 888999;

//--- Indicator Handles
int h_atr, h_atr_slow, h_volume;

//--- Risk Management Variables
double dailyStartBalance, weeklyStartBalance, peakEquity;
bool   dailyLossLimitHit = false, weeklyLossLimitHit = false;
bool   drawdownLimitHit = false, equityProtectionHit = false;
int    consecutiveLosses = 0;
datetime lastTradeTime = 0;
datetime lastDayCheck = 0, lastWeekCheck = 0;
datetime currentDay = 0;

//--- Asian Range Variables
struct AsianRange
{
    datetime startTime;
    datetime endTime;
    double   highPrice;
    double   lowPrice;
    double   rangePips;
    bool     isValid;
    bool     breakoutOccurred;
};
AsianRange currentRange;

//--- Position Management
struct AdvancedPosition
{
    ulong    ticket;
    datetime openTime;
    double   entryPrice;
    double   stopLoss;
    double   takeProfit;
    double   riskAmount;
    double   originalSL;
    bool     breakevenApplied;
    bool     trailingActive;
    bool     partial1Closed;
    bool     partial2Closed;
    ENUM_POSITION_TYPE type;
    double   highestProfit;
    double   lowestProfit;
};
AdvancedPosition managedPositions[];

//--- Market Analysis Variables
double currentATR = 0;
double averageATR = 0;
double volatilityRatio = 0;
double currentSpread = 0;
double liquidityScore = 0;

//+------------------------------------------------------------------+
//| Send notifications                                               |
//+------------------------------------------------------------------+
void SendNotification(string subject, string message)
{
    if(IsStopped()) return;
    
    if(InpEnableAlerts) Alert(subject + ": " + message);
    
    if(InpEnablePushNotifications)
    {
        string combined = subject + ": " + message;
        if(StringLen(combined) > 255) combined = StringSubstr(combined, 0, 255);
        SendNotification(combined);
    }
    
    if(InpEnableEmailNotifications) SendMail(subject, message);
    
    if(InpDetailedLogging) Print(subject + ": " + message);
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize magic number
    MagicNumber = InpMagicNumber;
    
    // Initialize indicators
    h_atr = iATR(_Symbol, PERIOD_CURRENT, InpATRPeriod);
    h_atr_slow = iMA(_Symbol, PERIOD_CURRENT, InpATRAveragePeriod, 0, MODE_SMA, h_atr);
    h_volume = iVolumes(_Symbol, PERIOD_CURRENT, VOLUME_TICK);
    
    if(h_atr == INVALID_HANDLE || h_atr_slow == INVALID_HANDLE || h_volume == INVALID_HANDLE)
    {
        Print("Error creating indicators");
        return INIT_FAILED;
    }
    
    // Initialize risk management
    dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    weeklyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    peakEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    
    // Initialize Asian range
    ResetAsianRange();
    
    Print("=== London Breakout Pro v1.0 Initialized ===");
    Print("Optimized for: ", _Symbol);
    Print("Risk per trade: ", InpRiskPercentage, "%");
    Print("Asian session: ", InpAsianStartHour, ":00 - ", InpAsianEndHour, ":00 GMT");
    Print("London session: ", InpLondonStartHour, ":00 - ", InpLondonEndHour, ":00 GMT");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    IndicatorRelease(h_atr);
    IndicatorRelease(h_atr_slow);
    IndicatorRelease(h_volume);
    
    Print("London Breakout Pro v1.0 Deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Update market analysis
    UpdateMarketAnalysis();
    
    // Manage existing positions
    ManagePositions();
    
    // Check for new opportunities on new bar
    static datetime lastBarTime = 0;
    datetime newBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(newBarTime > lastBarTime)
    {
        lastBarTime = newBarTime;
        
        // Daily/weekly checks
        CheckNewDay();
        CheckNewWeek();
        
        // Asian range detection
        UpdateAsianRange();
        
        // London breakout logic
        CheckLondonBreakout();
        
        // Time-based position management
        CheckTimeBasedManagement();
    }
}

//+------------------------------------------------------------------+
//| Update market analysis                                           |
//+------------------------------------------------------------------+
void UpdateMarketAnalysis()
{
    // Update ATR and volatility
    double atr_buffer[1], atr_slow_buffer[1];
    if(CopyBuffer(h_atr, 0, 0, 1, atr_buffer) > 0)
        currentATR = atr_buffer[0];
    
    if(CopyBuffer(h_atr_slow, 0, 0, 1, atr_slow_buffer) > 0)
        averageATR = atr_slow_buffer[0];
    
    volatilityRatio = (averageATR > 0) ? currentATR / averageATR : 1.0;
    
    // Update spread
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    currentSpread = (ask - bid) / (point * 10); // Convert to pips
    
    // Calculate liquidity score (simplified)
    liquidityScore = CalculateLiquidityScore();
}

//+------------------------------------------------------------------+
//| Calculate liquidity score                                       |
//+------------------------------------------------------------------+
double CalculateLiquidityScore()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int hour = dt.hour;
    
    double score = 0.0;
    
    // Time-based liquidity
    if(hour >= 7 && hour <= 17) score += 0.4;      // London hours
    if(hour >= 13 && hour <= 22) score += 0.3;     // NY hours
    if(hour >= 8 && hour <= 16) score += 0.2;      // Overlap
    
    // Volatility component
    if(volatilityRatio >= 1.0 && volatilityRatio <= 2.0) score += 0.1;
    
    return MathMin(score, 1.0);
}

//+------------------------------------------------------------------+
//| Reset Asian range                                               |
//+------------------------------------------------------------------+
void ResetAsianRange()
{
    currentRange.startTime = 0;
    currentRange.endTime = 0;
    currentRange.highPrice = 0;
    currentRange.lowPrice = 999999;
    currentRange.rangePips = 0;
    currentRange.isValid = false;
    currentRange.breakoutOccurred = false;
}

//+------------------------------------------------------------------+
//| Update Asian range detection                                    |
//+------------------------------------------------------------------+
void UpdateAsianRange()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int currentHour = dt.hour;
    
    // Check if we're in Asian session
    bool inAsianSession = IsAsianSession(currentHour);
    
    if(inAsianSession)
    {
        // Start new range if needed
        if(currentRange.startTime == 0)
        {
            currentRange.startTime = TimeCurrent();
            currentRange.highPrice = iHigh(_Symbol, PERIOD_CURRENT, 0);
            currentRange.lowPrice = iLow(_Symbol, PERIOD_CURRENT, 0);
            
            if(InpNotifyOnSetup)
                SendNotification("Asian Range Started", StringFormat("Range detection started at %s", TimeToString(TimeCurrent())));
        }
        
        // Update range boundaries
        double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
        double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);
        
        if(currentHigh > currentRange.highPrice) currentRange.highPrice = currentHigh;
        if(currentLow < currentRange.lowPrice) currentRange.lowPrice = currentLow;
    }
    else if(currentRange.startTime > 0 && !currentRange.isValid)
    {
        // Asian session ended, validate range
        ValidateAsianRange();
    }
}

//+------------------------------------------------------------------+
//| Check if current hour is in Asian session                       |
//+------------------------------------------------------------------+
bool IsAsianSession(int hour)
{
    if(InpAsianStartHour < InpAsianEndHour)
        return (hour >= InpAsianStartHour && hour < InpAsianEndHour);
    else
        return (hour >= InpAsianStartHour || hour < InpAsianEndHour);
}

//+------------------------------------------------------------------+
//| Validate Asian range                                            |
//+------------------------------------------------------------------+
void ValidateAsianRange()
{
    if(currentRange.startTime == 0) return;
    
    currentRange.endTime = TimeCurrent();
    
    // Calculate range in pips
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    currentRange.rangePips = (currentRange.highPrice - currentRange.lowPrice) / (point * 10);
    
    // Validate range size
    if(currentRange.rangePips >= InpMinRangePips && currentRange.rangePips <= InpMaxRangePips)
    {
        currentRange.isValid = true;
        
        if(InpNotifyOnSetup)
        {
            SendNotification("Valid Asian Range", 
                StringFormat("Range: %.1f pips, High: %.5f, Low: %.5f", 
                currentRange.rangePips, currentRange.highPrice, currentRange.lowPrice));
        }
    }
    else
    {
        if(InpDetailedLogging)
        {
            Print("Asian range invalid - Size: ", currentRange.rangePips, " pips");
        }
        ResetAsianRange();
    }
}

//+------------------------------------------------------------------+
//| Check for London breakout                                       |
//+------------------------------------------------------------------+
void CheckLondonBreakout()
{
    if(!currentRange.isValid || currentRange.breakoutOccurred) return;
    if(!IsRiskManaged()) return;
    
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int currentHour = dt.hour;
    
    // Check if we're in London session
    if(currentHour < InpLondonStartHour || currentHour >= InpLondonEndHour) return;
    
    // Check market conditions
    if(!AreMarketConditionsFavorable()) return;
    
    // Get current price
    double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 0);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double bufferPips = InpBreakoutBuffer * point * 10;
    
    // Check for breakouts
    bool bullishBreakout = currentPrice > (currentRange.highPrice + bufferPips);
    bool bearishBreakout = currentPrice < (currentRange.lowPrice - bufferPips);
    
    if(bullishBreakout && InpAllowBothDirections)
    {
        if(IsVolumeConfirmed() && CanOpenPosition())
        {
            ExecuteBreakoutTrade(ORDER_TYPE_BUY);
            currentRange.breakoutOccurred = true;
        }
    }
    else if(bearishBreakout && InpAllowBothDirections)
    {
        if(IsVolumeConfirmed() && CanOpenPosition())
        {
            ExecuteBreakoutTrade(ORDER_TYPE_SELL);
            currentRange.breakoutOccurred = true;
        }
    }
}

//+------------------------------------------------------------------+
//| Check if market conditions are favorable                        |
//+------------------------------------------------------------------+
bool AreMarketConditionsFavorable()
{
    // Volatility filter
    if(InpUseVolatilityFilter)
    {
        if(volatilityRatio < InpMinVolatilityRatio || volatilityRatio > InpMaxVolatilityRatio)
            return false;
    }
    
    // Spread filter
    if(InpUseSpreadFilter)
    {
        if(currentSpread > InpMaxSpreadPips)
            return false;
    }
    
    // Liquidity filter
    if(InpUseLiquidityFilter)
    {
        if(liquidityScore < InpMinLiquidityScore)
            return false;
    }
    
    // Time filters
    if(InpUseTimeFilters)
    {
        if(!IsGoodTradingTime())
            return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check volume confirmation                                       |
//+------------------------------------------------------------------+
bool IsVolumeConfirmed()
{
    if(!InpRequireVolumeConfirmation) return true;
    
    double volumes[];
    ArraySetAsSeries(volumes, true);
    
    if(CopyBuffer(h_volume, 0, 0, InpVolumeLookback + 1, volumes) < InpVolumeLookback + 1)
        return false;
    
    // Calculate average volume
    double totalVolume = 0;
    for(int i = 1; i <= InpVolumeLookback; i++)
        totalVolume += volumes[i];
    
    double avgVolume = totalVolume / InpVolumeLookback;
    
    return volumes[0] >= (avgVolume * InpVolumeMultiplier);
}

//+------------------------------------------------------------------+
//| Check if can open new position                                  |
//+------------------------------------------------------------------+
bool CanOpenPosition()
{
    // Check concurrent trades limit
    if(PositionsTotal() >= InpMaxConcurrentTrades) return false;
    
    // Check cooldown period
    if(lastTradeTime > 0)
    {
        if((TimeCurrent() - lastTradeTime) < (InpCooldownMinutes * 60))
            return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Execute breakout trade                                          |
//+------------------------------------------------------------------+
void ExecuteBreakoutTrade(ENUM_ORDER_TYPE orderType)
{
    double price = (orderType == ORDER_TYPE_BUY) ? 
        SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    // Calculate stop loss
    double sl = CalculateStopLoss(orderType);
    if(sl == 0) return;
    
    // Calculate take profit
    double tp = CalculateTakeProfit(orderType, price, sl);
    
    // Validate risk-reward
    double risk = MathAbs(price - sl);
    double reward = MathAbs(tp - price);
    double rr = (risk > 0) ? reward / risk : 0;
    
    if(rr < InpMinRiskReward)
    {
        if(InpDetailedLogging)
            Print("Trade rejected - R:R too low: ", rr);
        return;
    }
    
    // Calculate position size
    double lots = CalculatePositionSize(risk);
    if(lots <= 0) return;
    
    // Execute trade
    MqlTradeRequest request = {};
    MqlTradeResult result_info = {};
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lots;
    request.type = orderType;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = MagicNumber;
    request.comment = InpTradeComment;
    bool result = OrderSend(request, result_info);
    
    if(result)
    {
        lastTradeTime = TimeCurrent();
        
        if(InpNotifyOnBreakout)
        {
            SendNotification("London Breakout Trade", 
                StringFormat("%s breakout executed at %.5f, SL: %.5f, TP: %.5f, R:R: %.2f", 
                EnumToString(orderType), price, sl, tp, rr));
        }
    }
    else
    {
        SendNotification("Trade Execution Error", 
            StringFormat("Failed to execute %s trade. Error: %d", 
            EnumToString(orderType), result_info.retcode));
    }
}

//+------------------------------------------------------------------+
//| Calculate stop loss                                             |
//+------------------------------------------------------------------+
double CalculateStopLoss(ENUM_ORDER_TYPE orderType)
{
    double sl = 0;
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double atrBuffer = currentATR * 0.5;
    
    if(orderType == ORDER_TYPE_BUY)
    {
        // Place SL below Asian range low with ATR buffer
        sl = currentRange.lowPrice - atrBuffer;
    }
    else
    {
        // Place SL above Asian range high with ATR buffer  
        sl = currentRange.highPrice + atrBuffer;
    }
    
    return NormalizeDouble(sl, _Digits);
}

//+------------------------------------------------------------------+
//| Calculate take profit                                           |
//+------------------------------------------------------------------+
double CalculateTakeProfit(ENUM_ORDER_TYPE orderType, double price, double sl)
{
    double risk = MathAbs(price - sl);
    double baseTarget = risk * InpBaseRiskReward;
    
    double tp = 0;
    if(InpUseDynamicTP)
    {
        // Use ATR-based dynamic target
        double atrTarget = currentATR * 2.0;
        double dynamicReward = MathMax(baseTarget, atrTarget);
        
        // Cap at maximum R:R
        double maxReward = risk * InpMaxRiskReward;
        dynamicReward = MathMin(dynamicReward, maxReward);
        
        tp = (orderType == ORDER_TYPE_BUY) ? price + dynamicReward : price - dynamicReward;
    }
    else
    {
        tp = (orderType == ORDER_TYPE_BUY) ? price + baseTarget : price - baseTarget;
    }
    
    return NormalizeDouble(tp, _Digits);
}

//+------------------------------------------------------------------+
//| Calculate position size with advanced risk management           |
//+------------------------------------------------------------------+
double CalculatePositionSize(double risk)
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskPct = GetAdaptiveRisk();
    double riskAmount = balance * (riskPct / 100.0);
    
    // Apply pair-specific adjustment
    riskAmount *= GetPairSpecificMultiplier();
    
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    double lots = 0;
    if(tickValue > 0 && tickSize > 0)
    {
        double riskPerLot = (risk / tickSize) * tickValue;
        if(riskPerLot > 0) lots = riskAmount / riskPerLot;
    }
    
    // Apply position size limit
    double maxPositionValue = balance * (InpMaxPositionSize / 100.0);
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double contractSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_CONTRACT_SIZE);
    double maxLots = maxPositionValue / (currentPrice * contractSize);
    
    if(lots > maxLots) lots = maxLots;
    
    // Normalize lot size
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lots = MathRound(lots / lotStep) * lotStep;
    
    if(lots < minLot) lots = minLot;
    if(lots > maxLot) lots = maxLot;
    
    return lots;
}

//+------------------------------------------------------------------+
//| Get adaptive risk based on recent performance                   |
//+------------------------------------------------------------------+
double GetAdaptiveRisk()
{
    if(!InpUseAdaptiveRisk) return InpRiskPercentage;
    
    double baseRisk = InpRiskPercentage;
    
    // Reduce risk after consecutive losses
    if(consecutiveLosses >= 1) baseRisk *= 0.8;    // 20% reduction
    if(consecutiveLosses >= 2) baseRisk *= 0.6;    // 40% reduction  
    if(consecutiveLosses >= 3) baseRisk *= 0.4;    // 60% reduction
    
    return MathMax(baseRisk, 0.2); // Minimum 0.2%
}

//+------------------------------------------------------------------+
//| Get pair-specific multiplier                                    |
//+------------------------------------------------------------------+
double GetPairSpecificMultiplier()
{
    string symbol = _Symbol;
    
    // Adjust based on pair characteristics
    if(StringFind(symbol, "GBP") >= 0) return 0.8;     // GBP pairs more volatile
    if(StringFind(symbol, "JPY") >= 0) return 1.1;     // JPY pairs less volatile
    if(StringFind(symbol, "AUD") >= 0) return 0.9;     // AUD moderate volatility
    
    return InpPairSpecificMultiplier; // Default for EUR pairs
}

//+------------------------------------------------------------------+
//| Advanced position management                                     |
//+------------------------------------------------------------------+
void ManagePositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetInteger(POSITION_MAGIC) != InpMagicNumber) continue;
        if(PositionGetString(POSITION_SYMBOL) != _Symbol) continue;
        
        ManageAdvancedPosition(ticket);
    }
}

//+------------------------------------------------------------------+
//| Manage individual position with advanced features               |
//+------------------------------------------------------------------+
void ManageAdvancedPosition(ulong ticket)
{
    if(!PositionSelectByTicket(ticket)) return;
    
    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    double currentSL = PositionGetDouble(POSITION_SL);
    double currentTP = PositionGetDouble(POSITION_TP);
    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    
    double currentPrice = (posType == POSITION_TYPE_BUY) ?
        SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    // Find position in managed array
    int posIndex = FindManagedPosition(ticket);
    if(posIndex < 0) return;
    
    double risk = MathAbs(managedPositions[posIndex].originalSL - openPrice);
    double currentProfit = (posType == POSITION_TYPE_BUY) ?
        (currentPrice - openPrice) : (openPrice - currentPrice);
    
    double currentRR = (risk > 0) ? currentProfit / risk : 0;
    
    // Update profit tracking
    if(currentProfit > managedPositions[posIndex].highestProfit)
        managedPositions[posIndex].highestProfit = currentProfit;
    if(currentProfit < managedPositions[posIndex].lowestProfit)
        managedPositions[posIndex].lowestProfit = currentProfit;
    
    // Breakeven management
    if(InpUseBreakeven && !managedPositions[posIndex].breakevenApplied && 
       currentRR >= InpBreakevenRR)
    {
        ApplyBreakeven(ticket, posIndex, openPrice, posType, risk);
    }
    
    // Partial profit taking
    if(InpUsePartialClose)
    {
        HandlePartialClose(ticket, posIndex, currentRR, posType);
    }
    
    // Trailing stop
    if(InpUseTrailingStop && managedPositions[posIndex].breakevenApplied)
    {
        ApplyTrailingStop(ticket, posIndex, currentPrice, posType);
    }
}

//+------------------------------------------------------------------+
//| Apply breakeven                                                 |
//+------------------------------------------------------------------+
void ApplyBreakeven(ulong ticket, int posIndex, double openPrice, ENUM_POSITION_TYPE posType, double risk)
{
    double newSL = openPrice + ((posType == POSITION_TYPE_BUY ? 1 : -1) * InpBreakevenBuffer * risk);
    
    MqlTradeRequest mod_request = {};
    MqlTradeResult mod_result = {};
    mod_request.action = TRADE_ACTION_SLTP;
    mod_request.position = ticket;
    mod_request.sl = newSL;
    mod_request.tp = PositionGetDouble(POSITION_TP);
    if(OrderSend(mod_request, mod_result))
    {
        managedPositions[posIndex].breakevenApplied = true;
        
        if(InpNotifyOnClose)
            SendNotification("Breakeven Applied", StringFormat("Position #%d moved to breakeven", ticket));
    }
}

//+------------------------------------------------------------------+
//| Handle partial profit taking                                    |
//+------------------------------------------------------------------+
void HandlePartialClose(ulong ticket, int posIndex, double currentRR, ENUM_POSITION_TYPE posType)
{
    // First partial close
    if(!managedPositions[posIndex].partial1Closed && currentRR >= InpPartialClose1_RR)
    {
        double closePercent = InpPartialClose1_Percent / 100.0;
        double currentVolume = PositionGetDouble(POSITION_VOLUME);
        double closeVolume = NormalizeDouble(currentVolume * closePercent, 2);
        
        MqlTradeRequest close_request = {};
        MqlTradeResult close_result = {};
        close_request.action = TRADE_ACTION_DEAL;
        close_request.position = ticket;
        close_request.symbol = _Symbol;
        close_request.volume = closeVolume;
        close_request.type = (posType == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
        close_request.price = (posType == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        close_request.magic = MagicNumber;
        close_request.comment = "Partial Close 1";
        
        if(OrderSend(close_request, close_result))
        {
            managedPositions[posIndex].partial1Closed = true;
            SendNotification("Partial Close 1", StringFormat("Closed %.0f%% at %.2f R:R", InpPartialClose1_Percent, currentRR));
        }
    }
    
    // Second partial close
    if(!managedPositions[posIndex].partial2Closed && currentRR >= InpPartialClose2_RR)
    {
        double closePercent = InpPartialClose2_Percent / 100.0;
        double currentVolume = PositionGetDouble(POSITION_VOLUME);
        double closeVolume = NormalizeDouble(currentVolume * closePercent, 2);
        
        MqlTradeRequest close_request2 = {};
        MqlTradeResult close_result2 = {};
        close_request2.action = TRADE_ACTION_DEAL;
        close_request2.position = ticket;
        close_request2.symbol = _Symbol;
        close_request2.volume = closeVolume;
        close_request2.type = (posType == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
        close_request2.price = (posType == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        close_request2.magic = MagicNumber;
        close_request2.comment = "Partial Close 2";
        
        if(OrderSend(close_request2, close_result2))
        {
            managedPositions[posIndex].partial2Closed = true;
            SendNotification("Partial Close 2", StringFormat("Closed %.0f%% at %.2f R:R", InpPartialClose2_Percent, currentRR));
        }
    }
}

//+------------------------------------------------------------------+
//| Apply trailing stop                                             |
//+------------------------------------------------------------------+
void ApplyTrailingStop(ulong ticket, int posIndex, double currentPrice, ENUM_POSITION_TYPE posType)
{
    double newSL = 0;
    
    if(InpUseSwingTrailing)
    {
        newSL = CalculateSwingTrailingSL(posType);
    }
    else
    {
        // ATR-based trailing
        double trailDistance = currentATR * InpTrailATRMultiplier;
        newSL = currentPrice - ((posType == POSITION_TYPE_BUY ? 1 : -1) * trailDistance);
    }
    
    double currentSL = PositionGetDouble(POSITION_SL);
    bool shouldModify = (posType == POSITION_TYPE_BUY && newSL > currentSL) ||
                       (posType == POSITION_TYPE_SELL && newSL < currentSL);
    
    if(shouldModify)
    {
        MqlTradeRequest trail_request = {};
        MqlTradeResult trail_result = {};
        trail_request.action = TRADE_ACTION_SLTP;
        trail_request.position = ticket;
        trail_request.sl = NormalizeDouble(newSL, _Digits);
        trail_request.tp = PositionGetDouble(POSITION_TP);
        OrderSend(trail_request, trail_result);
    }
}

//+------------------------------------------------------------------+
//| Calculate swing-based trailing stop                             |
//+------------------------------------------------------------------+
double CalculateSwingTrailingSL(ENUM_POSITION_TYPE posType)
{
    double trailSL = 0;
    double buffer = currentATR * 0.5;
    
    if(posType == POSITION_TYPE_BUY)
    {
        int lowestBar = iLowest(_Symbol, InpTrailTimeframe, MODE_LOW, InpSwingLookback, 1);
        double swingLow = iLow(_Symbol, InpTrailTimeframe, lowestBar);
        trailSL = swingLow - buffer;
    }
    else
    {
        int highestBar = iHighest(_Symbol, InpTrailTimeframe, MODE_HIGH, InpSwingLookback, 1);
        double swingHigh = iHigh(_Symbol, InpTrailTimeframe, highestBar);
        trailSL = swingHigh + buffer;
    }
    
    return trailSL;
}

//+------------------------------------------------------------------+
//| Check if it's good trading time                                 |
//+------------------------------------------------------------------+
bool IsGoodTradingTime()
{
    // Add news avoidance, weekend close, etc.
    if(InpCloseBeforeWeekend)
    {
        MqlDateTime dt;
        TimeToStruct(TimeCurrent(), dt);
        if(dt.day_of_week == 5 && dt.hour >= InpFridayCloseHour) // Friday
            return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Advanced risk management checks                                 |
//+------------------------------------------------------------------+
bool IsRiskManaged()
{
    // Daily loss limit
    if(dailyLossLimitHit) return false;
    
    // Weekly loss limit  
    if(weeklyLossLimitHit) return false;
    
    // Drawdown protection
    if(drawdownLimitHit) return false;
    
    // Equity protection
    if(equityProtectionHit) return false;
    
    // Check current equity vs peak
    double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    if(currentEquity > peakEquity) peakEquity = currentEquity;
    
    double equityDrawdown = (peakEquity - currentEquity) / peakEquity * 100.0;
    if(equityDrawdown >= InpMaxDrawdownPercent)
    {
        drawdownLimitHit = true;
        SendNotification("Drawdown Limit Hit", StringFormat("Trading paused - Drawdown: %.1f%%", equityDrawdown));
        return false;
    }
    
    // Equity protection check
    double equityPercent = currentEquity / peakEquity * 100.0;
    if(equityPercent < InpEquityProtection)
    {
        equityProtectionHit = true;
        SendNotification("Equity Protection", StringFormat("Trading stopped - Equity below %.1f%% of peak", InpEquityProtection));
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Find managed position index                                     |
//+------------------------------------------------------------------+
int FindManagedPosition(ulong ticket)
{
    for(int i = 0; i < ArraySize(managedPositions); i++)
    {
        if(managedPositions[i].ticket == ticket)
            return i;
    }
    return -1;
}

//+------------------------------------------------------------------+
//| Time-based position management                                  |
//+------------------------------------------------------------------+
void CheckTimeBasedManagement()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetInteger(POSITION_MAGIC) != InpMagicNumber) continue;
        
        datetime openTime = (datetime)PositionGetInteger(POSITION_TIME);
        int hoursOpen = (int)((TimeCurrent() - openTime) / 3600);
        
        if(hoursOpen >= InpMaxTradeHours)
        {
            MqlTradeRequest time_close_request = {};
            MqlTradeResult time_close_result = {};
            time_close_request.action = TRADE_ACTION_DEAL;
            time_close_request.position = ticket;
            time_close_request.symbol = _Symbol;
            time_close_request.volume = PositionGetDouble(POSITION_VOLUME);
            time_close_request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
            time_close_request.price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            time_close_request.magic = MagicNumber;
            time_close_request.comment = "Time-based Close";
            OrderSend(time_close_request, time_close_result);
            SendNotification("Time-based Close", StringFormat("Position #%d closed after %d hours", ticket, hoursOpen));
        }
    }
}

//+------------------------------------------------------------------+
//| Handle trade transactions                                       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                        const MqlTradeRequest& request,
                        const MqlTradeResult& result)
{
    if(trans.type != TRADE_TRANSACTION_DEAL_ADD) return;
    if(!HistoryDealSelect(trans.deal)) return;
    if(HistoryDealGetInteger(trans.deal, DEAL_MAGIC) != InpMagicNumber) return;

    long dealEntry = HistoryDealGetInteger(trans.deal, DEAL_ENTRY);
    ulong positionId = HistoryDealGetInteger(trans.deal, DEAL_POSITION_ID);

    if(dealEntry == DEAL_ENTRY_IN)
    {
        // New position opened
        AddManagedPosition(positionId);
    }
    else if(dealEntry == DEAL_ENTRY_OUT)
    {
        // Position closed
        if(!PositionSelectByTicket(positionId))
        {
            HandlePositionClosed(trans.deal, positionId);
        }
    }
}

//+------------------------------------------------------------------+
//| Add new managed position                                        |
//+------------------------------------------------------------------+
void AddManagedPosition(ulong ticket)
{
    if(!PositionSelectByTicket(ticket)) return;
    
    AdvancedPosition newPos;
    newPos.ticket = ticket;
    newPos.openTime = (datetime)PositionGetInteger(POSITION_TIME);
    newPos.entryPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    newPos.stopLoss = PositionGetDouble(POSITION_SL);
    newPos.takeProfit = PositionGetDouble(POSITION_TP);
    newPos.originalSL = newPos.stopLoss;
    newPos.type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    newPos.breakevenApplied = false;
    newPos.trailingActive = false;
    newPos.partial1Closed = false;
    newPos.partial2Closed = false;
    newPos.highestProfit = 0;
    newPos.lowestProfit = 0;
    
    int size = ArraySize(managedPositions);
    ArrayResize(managedPositions, size + 1);
    managedPositions[size] = newPos;
}

//+------------------------------------------------------------------+
//| Handle position closed                                          |
//+------------------------------------------------------------------+
void HandlePositionClosed(ulong deal, ulong positionId)
{
    double profit = HistoryDealGetDouble(deal, DEAL_PROFIT);
    
    // Update consecutive losses counter
    if(profit < 0) consecutiveLosses++;
    else consecutiveLosses = 0;
    
    // Notification
    if(InpNotifyOnClose)
    {
        SendNotification("Position Closed", 
            StringFormat("Position #%d closed with profit: %.2f", positionId, profit));
    }
    
    // Check risk limits
    CheckDailyLoss();
    CheckWeeklyLoss();
    
    // Remove from managed positions
    RemoveManagedPosition(positionId);
}

//+------------------------------------------------------------------+
//| Remove managed position                                         |
//+------------------------------------------------------------------+
void RemoveManagedPosition(ulong ticket)
{
    for(int i = ArraySize(managedPositions) - 1; i >= 0; i--)
    {
        if(managedPositions[i].ticket == ticket)
        {
            ArrayRemove(managedPositions, i, 1);
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Check daily loss limit                                          |
//+------------------------------------------------------------------+
void CheckDailyLoss()
{
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double dailyLoss = (dailyStartBalance - currentBalance) / dailyStartBalance * 100.0;
    
    if(dailyLoss >= InpMaxDailyLoss)
    {
        dailyLossLimitHit = true;
        SendNotification("Daily Loss Limit", StringFormat("Daily loss limit %.1f%% reached", InpMaxDailyLoss));
    }
}

//+------------------------------------------------------------------+
//| Check weekly loss limit                                         |
//+------------------------------------------------------------------+
void CheckWeeklyLoss()
{
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double weeklyLoss = (weeklyStartBalance - currentBalance) / weeklyStartBalance * 100.0;
    
    if(weeklyLoss >= InpMaxWeeklyLoss)
    {
        weeklyLossLimitHit = true;
        SendNotification("Weekly Loss Limit", StringFormat("Weekly loss limit %.1f%% reached", InpMaxWeeklyLoss));
    }
}

//+------------------------------------------------------------------+
//| Check new day                                                   |
//+------------------------------------------------------------------+
void CheckNewDay()
{
    datetime newDay = iTime(_Symbol, PERIOD_D1, 0);
    if(newDay > lastDayCheck)
    {
        dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        dailyLossLimitHit = false;
        lastDayCheck = newDay;
        currentDay = newDay;
        
        // Reset Asian range for new day
        ResetAsianRange();
        
        Print("New trading day - Daily limits reset");
    }
}

//+------------------------------------------------------------------+
//| Check new week                                                  |
//+------------------------------------------------------------------+
void CheckNewWeek()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    if(dt.day_of_week == 1 && lastWeekCheck < currentDay) // Monday
    {
        weeklyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        weeklyLossLimitHit = false;
        drawdownLimitHit = false;
        equityProtectionHit = false;
        lastWeekCheck = TimeCurrent();
        
        Print("New trading week - Weekly limits reset");
    }
}