//+------------------------------------------------------------------+
//|                        CCI_Trend_Continuation_v2.mq5             |
//|                      Copyright 2025, Gemini Trading Systems      |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Gemini Trading Systems"
#property link      "https://www.google.com"
#property version   "1.11" // Version updated for compilation fix

#include <Trade/Trade.mqh>

//--- Input Parameters
//--- Strategy Settings
input group "=== Strategy Settings ===="
input ENUM_TIMEFRAMES InpExecutionTimeframe = PERIOD_M15; // Timeframe for Entries/Exits
input ENUM_TIMEFRAMES InpTrendTimeframe = PERIOD_H1;      // Timeframe for Trend Direction
input int             InpCCI_Period = 14;               // CCI Period
input int             InpATR_Period = 14;               // ATR Period for Volatility
input int             InpATR_SMA_Period = 50;           // SMA of ATR Period
input int             InpEMA_Period = 200;              // Trend EMA Period
input int             InpSwingLookback = 15;            // Lookback for SL (bars)

//--- Risk Management
input group "=== Risk Management ===="
input double          InpRiskPercentage = 1.0;          // Risk per trade as % of balance
input double          InpMaxDailyLoss = 3.0;            // Max daily loss in %
input int             InpMaxConcurrentTrades = 2;       // Maximum open trades
input double          InpMaxStopLossATRMultiplier = 4.0;// Max SL distance in multiples of ATR

//--- Exit Settings
enum ENUM_TP_MODE
{
    TP_MODE_FIXED_RR,     // T/P based on a fixed Risk:Reward ratio
    TP_MODE_CCI_CROSS,    // Exit is managed by CCI crossing the zero line
    TP_MODE_ATR_MULTIPLE  // T/P is based on a multiple of the current ATR
};

input group "=== Exit Settings ===="
input ENUM_TP_MODE    InpTakeProfitMode = TP_MODE_CCI_CROSS; // Take Profit Mode
input double          InpFixedRiskReward = 2.0;         // R:R for 'Fixed R:R' mode
input double          InpAtrTpMultiplier = 3.0;         // ATR Multiplier for 'ATR Multiple' mode
input double          InpFailsafeRiskReward = 5.0;      // Failsafe R:R for 'CCI Cross' mode (0=disabled)
input bool            InpUseBreakeven = true;           // Enable Breakeven
input double          InpBreakevenPips = 15.0;          // Pips in profit to move to breakeven
input double          InpBreakevenBufferPips = 2.0;     // Buffer in pips for breakeven SL
input bool            InpUseTrailingStop = true;        // Enable Trailing Stop
input double          InpTrailStartPips = 25.0;         // Pips in profit to activate trailing SL
input double          InpTrailDistanceATR = 2.5;        // Trailing distance in ATR multiples

//--- Notification Settings
input group "=== Notification Settings ===="
input bool   InpEnableAlerts = true;            // Enable On-Screen Alerts
input bool   InpEnablePushNotifications = false; // Enable Push Notifications (to mobile)
input bool   InpEnableEmailNotifications = false; // Enable Email Notifications
input bool   InpNotifyOnOpen = true;            // Notify on New Trade Open
input bool   InpNotifyOnClose = true;           // Notify on Trade Close
input bool   InpNotifyOnError = true;           // Notify on Trading Error

//--- General Settings
input group "=== General Settings ===="
input ulong           InpMagicNumber = 13579;           // EA's Magic Number
input string          InpTradeComment = "CCI_Trend_v2"; // Trade Comment

//--- Global Variables
//--- Indicator Handles
int h_ema_trend;
int h_cci_exec;
int h_atr_exec;
int h_atr_sma_exec;

//--- Trading Object
CTrade trade;

//--- Risk Management Variables
double dailyStartBalance;
bool   dailyLossLimitReached = false;
datetime lastDayCheck = 0;

//--- Position State Tracking for Breakeven/Trailing
struct PositionState
{
   ulong   ticket;
   bool    breakeven_applied;
};
PositionState managedPositions[];

//+------------------------------------------------------------------+
//| Send Notifications                                               |
//+------------------------------------------------------------------+
void SendNotification(string subject, string message)
{
    if(IsStopped()) return; // Don't send notifications during deinitialization

    if(InpEnableAlerts)
    {
        Alert(subject, "\n", message);
    }
    if(InpEnablePushNotifications)
    {
        // MQL5 SendNotification has a 255 character limit
        string combined_message = subject + ": " + message;
        if(StringLen(combined_message) > 255) 
            combined_message = StringSubstr(combined_message, 0, 255);
        SendNotification(combined_message);
    }
    if(InpEnableEmailNotifications)
    {
        SendMail(subject, message);
    }
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Initialize trading object
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetTypeFillingBySymbol(_Symbol);

    //--- Initialize Trend Indicator (H1)
    h_ema_trend = iMA(_Symbol, InpTrendTimeframe, InpEMA_Period, 0, MODE_EMA, PRICE_CLOSE);
    if(h_ema_trend == INVALID_HANDLE)
    {
        Print("Error creating trend EMA indicator. Error code: ", GetLastError());
        return(INIT_FAILED);
    }

    //--- Initialize Execution Indicators (M15/M5)
    h_cci_exec = iCCI(_Symbol, InpExecutionTimeframe, InpCCI_Period, PRICE_TYPICAL);
    if(h_cci_exec == INVALID_HANDLE)
    {
        Print("Error creating CCI indicator. Error code: ", GetLastError());
        return(INIT_FAILED);
    }

    h_atr_exec = iATR(_Symbol, InpExecutionTimeframe, InpATR_Period);
    if(h_atr_exec == INVALID_HANDLE)
    {
        Print("Error creating ATR indicator. Error code: ", GetLastError());
        return(INIT_FAILED);
    }
    
    //--- CORRECTED: Create an indicator based on another indicator's data
    h_atr_sma_exec = iMA(_Symbol, InpExecutionTimeframe, InpATR_SMA_Period, 0, MODE_SMA, h_atr_exec);
    if(h_atr_sma_exec == INVALID_HANDLE)
    {
        Print("Error creating SMA on ATR indicator. Error code: ", GetLastError());
        return(INIT_FAILED);
    }

    //--- Initialize risk management
    dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    
    Print("CCI Trend-Continuation v2 EA Initialized Successfully.");
    Print("Notifications: Alerts=", InpEnableAlerts, ", Push=", InpEnablePushNotifications, ", Email=", InpEnableEmailNotifications);

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release indicator handles
    IndicatorRelease(h_ema_trend);
    IndicatorRelease(h_cci_exec);
    IndicatorRelease(h_atr_exec);
    IndicatorRelease(h_atr_sma_exec);
    Print("EA Deinitialized.");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Manage exits on every tick for precision
    ManageTradeExits();

    //--- Run logic only on a new bar of the execution timeframe
    static datetime lastBarTime = 0;
    datetime newBarTime = (datetime)SeriesInfoInteger(_Symbol, InpExecutionTimeframe, SERIES_LASTBAR_DATE);

    if(newBarTime > lastBarTime)
    {
        lastBarTime = newBarTime;

        //--- Check if a new day has started to reset limits
        CheckNewDay();

        //--- Check for dynamic exits only if in CCI Cross mode
        if(InpTakeProfitMode == TP_MODE_CCI_CROSS)
        {
            ManageDynamicExits();
        }

        //--- Check for new trade opportunities
        CheckForNewTrade();
    }
}

//+------------------------------------------------------------------+
//| Check for new trade signals                                      |
//+------------------------------------------------------------------+
void CheckForNewTrade()
{
    //--- Check if trading is allowed
    if(!IsTradingAllowed())
    {
        return;
    }

    //--- Get Trend Direction
    int trend = GetTrendDirection();
    if(trend == 0) return;

    //--- Check Volatility Filter
    if(!IsVolatilityHigh()) return;

    //--- Check for CCI Cross Signal
    int signal = CheckCCI_Cross();

    //--- Match trend with signal
    if(trend == 1 && signal == 1) // Bullish trend and bullish signal
    {
        ExecuteTrade(ORDER_TYPE_BUY);
    }
    else if(trend == -1 && signal == -1) // Bearish trend and bearish signal
    {
        ExecuteTrade(ORDER_TYPE_SELL);
    }
}

//+------------------------------------------------------------------+
//| Manage dynamic exits for open positions                          |
//+------------------------------------------------------------------+
void ManageDynamicExits()
{
    double cci_buffer[2];
    if(CopyBuffer(h_cci_exec, 0, 0, 2, cci_buffer) < 2) return;
    double prev_cci = cci_buffer[1];
    double curr_cci = cci_buffer[0];

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetInteger(POSITION_MAGIC) == InpMagicNumber && PositionGetString(POSITION_SYMBOL) == _Symbol)
        {
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

            // Exit BUY if CCI crosses below zero (momentum waning)
            if(posType == POSITION_TYPE_BUY && prev_cci > 0 && curr_cci <= 0)
            {
                trade.PositionClose(ticket);
            } // Exit SELL if CCI crosses above zero (momentum waning)
            else if(posType == POSITION_TYPE_SELL && prev_cci < 0 && curr_cci >= 0)
            {
                trade.PositionClose(ticket);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Manage Breakeven and Trailing Stops for Open Positions           |
//+------------------------------------------------------------------+
void ManageTradeExits()
{
    if(!InpUseBreakeven && !InpUseTrailingStop) return;

    double atr_buffer[];
    if(InpUseTrailingStop)
    {
      ArraySetAsSeries(atr_buffer, true);
      if(CopyBuffer(h_atr_exec, 0, 0, 1, atr_buffer) < 1) return;
    }
    double current_atr = InpUseTrailingStop ? atr_buffer[0] : 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetInteger(POSITION_MAGIC) != InpMagicNumber || PositionGetString(POSITION_SYMBOL) != _Symbol) continue;

        // Find the corresponding managed state for this position
        int pos_idx = -1;
        for(int j = 0; j < ArraySize(managedPositions); j++)
        {
            if(managedPositions[j].ticket == ticket) { pos_idx = j; break; }
        }

        // If position is not tracked, add it now to be managed.
        // This makes the EA robust to restarts.
        if(pos_idx == -1)
        {
            PositionState newPos;
            newPos.ticket = ticket;
            newPos.breakeven_applied = false; // Assume not applied for existing trades
            int size = ArraySize(managedPositions);
            ArrayResize(managedPositions, size + 1);
            managedPositions[size] = newPos;
            pos_idx = size;
            Print("Discovered and now managing existing position #", ticket);
        }

        if(pos_idx != -1)
        {
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_sl = PositionGetDouble(POSITION_SL);
            ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
            double current_price = SymbolInfoDouble(_Symbol, (type == POSITION_TYPE_BUY) ? SYMBOL_BID : SYMBOL_ASK);
            double profit_pips = (type == POSITION_TYPE_BUY) ? (current_price - open_price) / point / 10 : (open_price - current_price) / point / 10;

            // --- Breakeven Logic ---
            if (InpUseBreakeven && !managedPositions[pos_idx].breakeven_applied && profit_pips >= InpBreakevenPips)
            {
                double breakeven_sl = open_price + ((type == POSITION_TYPE_BUY ? 1 : -1) * InpBreakevenBufferPips * point * 10);
                bool should_modify = (type == POSITION_TYPE_BUY && breakeven_sl > current_sl) || (type == POSITION_TYPE_SELL && breakeven_sl < current_sl);

                if (should_modify && trade.PositionModify(ticket, breakeven_sl, PositionGetDouble(POSITION_TP)))
                {
                    managedPositions[pos_idx].breakeven_applied = true;
                    if(InpNotifyOnOpen) SendNotification("Breakeven", StringFormat("Position #%d moved to breakeven.", ticket));
                }
            }

            // --- Trailing Stop Logic ---
            if (InpUseTrailingStop && profit_pips > InpTrailStartPips)
            {
                double new_sl = current_price + ((type == POSITION_TYPE_BUY ? -1 : 1) * current_atr * InpTrailDistanceATR);
                bool should_modify = (type == POSITION_TYPE_BUY && new_sl > current_sl) || (type == POSITION_TYPE_SELL && new_sl < current_sl);

                if(should_modify)
                {
                    trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
                }
            }
        }
    }
}


//+------------------------------------------------------------------+
//| Execute a trade                                                  |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE orderType)
{
    double price = (orderType == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    double sl = CalculateStopLoss(orderType);
    if(sl == 0)
    {
        if(InpNotifyOnError) SendNotification("Trade Error: " + _Symbol, "Could not calculate a valid Stop Loss. Aborting trade.");
        return;
    }

    double atr_val[1];
    if(CopyBuffer(h_atr_exec, 0, 0, 1, atr_val) < 1) return;
    double current_atr = atr_val[0];
    
    if(MathAbs(price - sl) > InpMaxStopLossATRMultiplier * current_atr)
    {
        string msg = StringFormat("Stop Loss distance is too large (>%.1f*ATR). Signal ignored.", InpMaxStopLossATRMultiplier);
        if(InpNotifyOnError) SendNotification("Trade Warning: " + _Symbol, msg);
        return;
    }

    double tp = 0;

    // --- Calculate Take Profit based on selected mode ---
    switch(InpTakeProfitMode)
    {
        case TP_MODE_FIXED_RR:
        {
            double sl_distance = MathAbs(price - sl);
            tp = (orderType == ORDER_TYPE_BUY) ? price + sl_distance * InpFixedRiskReward : price - sl_distance * InpFixedRiskReward;
            break;
        }

        case TP_MODE_ATR_MULTIPLE:
        {
            tp = (orderType == ORDER_TYPE_BUY) ? price + current_atr * InpAtrTpMultiplier : price - current_atr * InpAtrTpMultiplier;
            break;
        }

        case TP_MODE_CCI_CROSS:
        {
            if(InpFailsafeRiskReward > 0)
            {
                double sl_dist_failsafe = MathAbs(price - sl);
                // This TP is set far away to act as a safety net if the EA fails
                tp = (orderType == ORDER_TYPE_BUY) ? price + sl_dist_failsafe * InpFailsafeRiskReward : price - sl_dist_failsafe * InpFailsafeRiskReward;
            }
            // if failsafe is 0, tp remains 0
            break;
        }
    }

    if(tp != 0) tp = NormalizeDouble(tp, _Digits);

    double lots = CalculatePositionSize(sl);
    if(lots <= 0)
    {
        if(InpNotifyOnError) SendNotification("Trade Error: " + _Symbol, "Could not calculate a valid position size. Aborting trade.");
        return;
    }

    bool result = false;
    if(orderType == ORDER_TYPE_BUY)
    {
        result = trade.Buy(lots, _Symbol, price, sl, tp, InpTradeComment);
    }
    else
    {
        result = trade.Sell(lots, _Symbol, price, sl, tp, InpTradeComment);
    }

    if(result)
    {
        string subject = "New Trade Opened: " + _Symbol;
        string msg = StringFormat("%s @ %.5f, SL: %.5f, TP: %.5f (Mode: %s), Lots: %.2f",
                                  EnumToString(orderType), price, sl, tp, lots);
        if(InpNotifyOnOpen) SendNotification(subject, msg);
    }
    else
    {
        string subject = "Trade Execution Error: " + _Symbol;
        string msg = StringFormat("Failed to open %s trade. Error %d: %s",
                                  EnumToString(orderType), trade.ResultRetcode(), trade.ResultComment());
        if(InpNotifyOnError) SendNotification(subject, msg);
    }
}


//+------------------------------------------------------------------+
//| Check if trading is allowed based on risk rules                  |
//+------------------------------------------------------------------+
bool IsTradingAllowed()
{
    if(dailyLossLimitReached) return false;

    if(PositionsTotal() >= InpMaxConcurrentTrades) return false;

    return true;
}

//+------------------------------------------------------------------+
//| Determine trend direction from H1 chart                          |
//+------------------------------------------------------------------+
int GetTrendDirection()
{
    double ema_buffer[1];
    if(CopyBuffer(h_ema_trend, 0, 0, 1, ema_buffer) < 1) return 0;
    
    MqlRates rates[];
    if(CopyRates(_Symbol, InpTrendTimeframe, 0, 1, rates) < 1) return 0;
    
    double trend_price = rates[0].close;
    double trend_ema = ema_buffer[0];

    if(trend_price > trend_ema) return 1;  // Bullish
    if(trend_price < trend_ema) return -1; // Bearish
    
    return 0; // Neutral
}

//+------------------------------------------------------------------+
//| Check if volatility is high using ATR                            |
//+------------------------------------------------------------------+
bool IsVolatilityHigh()
{
    double atr_buffer[1];
    double sma_atr_buffer[1];

    if(CopyBuffer(h_atr_exec, 0, 0, 1, atr_buffer) < 1) return false;
    if(CopyBuffer(h_atr_sma_exec, 0, 0, 1, sma_atr_buffer) < 1) return false;

    return (atr_buffer[0] > sma_atr_buffer[0]);
}

//+------------------------------------------------------------------+
//| Check for CCI crossing back from overbought/oversold             |
//+------------------------------------------------------------------+
int CheckCCI_Cross()
{
    double cci_buffer[2];
    if(CopyBuffer(h_cci_exec, 0, 0, 2, cci_buffer) < 2) return 0;

    double prev_cci = cci_buffer[1];
    double curr_cci = cci_buffer[0];

    if(prev_cci <= -100 && curr_cci > -100) return 1;
    if(prev_cci >= 100 && curr_cci < 100) return -1;

    return 0; // No signal
}

//+------------------------------------------------------------------+
//| Calculate Stop Loss based on recent swing high/low               |
//+------------------------------------------------------------------+
double CalculateStopLoss(ENUM_ORDER_TYPE orderType)
{
    MqlRates rates[];
    if(CopyRates(_Symbol, InpExecutionTimeframe, 1, InpSwingLookback, rates) < InpSwingLookback) return 0;

    double result_sl = 0;
    double buffer = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE) * 10;

    if(orderType == ORDER_TYPE_BUY)
    {
        double lowest_low = rates[0].low;
        for(int i = 1; i < InpSwingLookback; i++) { if(rates[i].low < lowest_low) lowest_low = rates[i].low; }
        result_sl = lowest_low - buffer;
    }
    else
    {
        double highest_high = rates[0].high;
        for(int i = 1; i < InpSwingLookback; i++) { if(rates[i].high > highest_high) highest_high = rates[i].high; }
        result_sl = highest_high + buffer;
    }

    return NormalizeDouble(result_sl, _Digits);
}

//+------------------------------------------------------------------+
//| Calculate Position Size based on risk percentage                 |
//+------------------------------------------------------------------+
double CalculatePositionSize(double stopLoss)
{
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * (InpRiskPercentage / 100.0);
    
    double price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    if(stopLoss > price) price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    double sl_distance = MathAbs(price - stopLoss);
    if(sl_distance == 0) return 0;

    double lot_size = 0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    if(tick_value > 0 && tick_size > 0)
    {
        double risk_per_lot = (sl_distance / tick_size) * tick_value;
        if(risk_per_lot > 0) lot_size = risk_amount / risk_per_lot;
    }

    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathRound(lot_size / lot_step) * lot_step;
    
    if(lot_size < min_lot) lot_size = min_lot;
    if(lot_size > max_lot) lot_size = max_lot;

    return lot_size;
}

//+------------------------------------------------------------------+
//| Handle trade events to track daily profit/loss and notify        |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                        const MqlTradeRequest& request,
                        const MqlTradeResult& result)
{
    if(trans.type != TRADE_TRANSACTION_DEAL_ADD) return;
    if(!HistoryDealSelect(trans.deal)) return;
    if(HistoryDealGetInteger(trans.deal, DEAL_MAGIC) != InpMagicNumber) return;

    long deal_entry = HistoryDealGetInteger(trans.deal, DEAL_ENTRY);
    ulong position_id = HistoryDealGetInteger(trans.deal, DEAL_POSITION_ID);

    // A new position was opened
    if(deal_entry == DEAL_ENTRY_IN)
    {
        PositionState newPos;
        newPos.ticket = position_id;
        newPos.breakeven_applied = false;

        int size = ArraySize(managedPositions);
        ArrayResize(managedPositions, size + 1);
        managedPositions[size] = newPos;
    }
    // A position was fully closed
    else if(deal_entry == DEAL_ENTRY_OUT)
    {
        if(!PositionSelectByTicket(position_id)) // Check if it's fully closed
        {
            double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
            string symbol = HistoryDealGetString(trans.deal, DEAL_SYMBOL);

            if(InpNotifyOnClose)
            {
                string subject = "Trade Closed: " + symbol;
                string msg = StringFormat("Position #%d closed with profit: %.2f", position_id, profit);
                SendNotification(subject, msg);
            }

            if(!dailyLossLimitReached)
            {
                double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
                double daily_loss_pct = (dailyStartBalance - current_balance) / dailyStartBalance * 100.0;

                if(daily_loss_pct >= InpMaxDailyLoss)
                {
                    dailyLossLimitReached = true;
                    string subject = "RISK ALERT: Daily Loss Limit Hit";
                    string msg = StringFormat("Daily loss limit of %.2f%% reached. Trading disabled.", InpMaxDailyLoss);
                    Print(subject, " ", msg);
                    if(InpNotifyOnError) SendNotification(subject, msg);
                }
            }
            
            // Remove from managed array
            for(int i = ArraySize(managedPositions) - 1; i >= 0; i--)
            {
                if(managedPositions[i].ticket == position_id)
                {
                    ArrayRemove(managedPositions, i, 1);
                    break;
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Reset daily counters at the start of a new day                   |
//+------------------------------------------------------------------+
void CheckNewDay()
{
    datetime current_day_start = iTime(_Symbol, PERIOD_D1, 0);
    if(current_day_start > lastDayCheck)
    {
        dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        dailyLossLimitReached = false;
        Print("New trading day started. Daily loss limit has been reset.");
        lastDayCheck = current_day_start;
    }
}