//|                            ForexSwingTradingEA_BacktestFixed.mq5 |
//|                                 https://www.example.com          |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Ultimate Swing Trading EA"
#property link      "https://www.example.com"
#property version   "2.10"

#include <Trade/Trade.mqh>

//--- Input parameters
input group "=== Multi-Timeframe Settings ==="
input ENUM_TIMEFRAMES InpHigherTimeframe1 = PERIOD_D1;     // Higher timeframe 1 (Trend)
input ENUM_TIMEFRAMES InpHigherTimeframe2 = PERIOD_W1;     // Higher timeframe 2 (Major trend)
input bool   InpUseMTFConfirmation = false;                // Use multi-timeframe confirmation (DISABLED FOR TESTING)
input bool   InpRequireAllTFAlignment = false;             // Require all timeframes aligned (DISABLED FOR TESTING)
input int    InpMTFTrendStrength = 33;                     // MTF trend strength threshold (%) (REDUCED FROM 70)

input group "=== Risk Management ==="
input double InpRiskPercentage = 1.0;           // Risk per trade (%)
input double InpMaxDailyLoss = 20.0;            // Maximum daily loss (%) (INCREASED FOR TESTING)
input double InpMaxWeeklyLoss = 30.0;           // Maximum weekly loss (%) (INCREASED FOR TESTING)
input double InpMaxMonthlyLoss = 50.0;          // Maximum monthly loss (%) (INCREASED FOR TESTING)
input int    InpMaxCorrelatedPairs = 10;        // Max correlated pairs (INCREASED)
input int    InpMaxConsecutiveLosses = 20;      // Max consecutive losses (INCREASED)

input group "=== Strategy Parameters ==="
input double InpMinRiskReward = 1.5;            // Minimum risk:reward ratio (REDUCED)
input double InpTargetRiskReward = 2.0;         // Target risk:reward ratio (REDUCED)
input double InpStopLossATRMultiplier = 1.5;    // SL distance in ATR multiples
input int    InpMaxTradesPerDay = 10;           // Maximum trades per day (INCREASED)
input int    InpMaxTradesPerWeek = 50;          // Maximum trades per week (INCREASED)

input group "=== Technical Indicators ==="
input int    InpEMA20Period = 20;               // EMA 20 Period
input int    InpEMA50Period = 50;               // EMA 50 Period
input int    InpEMA200Period = 200;             // EMA 200 Period
input int    InpRSIPeriod = 14;                 // RSI Period
input int    InpATRPeriod = 14;                 // ATR Period for SL and Trailing
input int    InpMACDFast = 12;                  // MACD Fast Period
input int    InpMACDSlow = 26;                  // MACD Slow Period
input int    InpMACDSignal = 9;                 // MACD Signal Period

input group "=== Trading Hours ==="
input int    InpStartHour = 0;                  // Trading start hour (GMT) (EXPANDED)
input int    InpEndHour = 23;                   // Trading end hour (GMT) (EXPANDED)
input bool   InpTradeOnFriday = true;           // Trade on Friday (ENABLED FOR TESTING)

input group "=== News Filter ==="
input bool   InpUseNewsFilter = false;          // Use news filter (DISABLED FOR TESTING)
input int    InpNewsBufferMinutes = 30;         // News buffer minutes (REDUCED)

input group "=== Debugging ==="
input bool   InpEnableDebugMode = true;         // Enable debug mode
input bool   InpShowMTFInfo = true;             // Show MTF analysis info
input bool   InpShowEntryConditions = true;     // Show entry condition details
input bool   InpRelaxedMode = true;             // Use relaxed entry conditions

input group "=== Notification Settings ==="
input bool   InpEnablePushNotifications = false;   // Enable push notifications (DISABLED FOR TESTING)
input bool   InpEnableEmailNotifications = false;  // Enable email notifications (DISABLED FOR TESTING)
input bool   InpEnableSoundAlerts = false;         // Enable sound alerts (DISABLED FOR TESTING)
input bool   InpEnableVisualAlerts = false;        // Enable visual alerts (DISABLED FOR TESTING)
input bool   InpNotifyOnEntry = false;             // Notify on trade entry (DISABLED FOR TESTING)
input bool   InpNotifyOnExit = false;              // Notify on trade exit (DISABLED FOR TESTING)
input bool   InpNotifyOnRiskLimits = false;        // Notify on risk limit hits (DISABLED FOR TESTING)
input bool   InpNotifyOnTrailingStop = false;      // Notify on trailing stop updates (DISABLED FOR TESTING)
input bool   InpNotifyDailySummary = false;        // Send daily summary (DISABLED FOR TESTING)
input bool   InpNotifyWeeklySummary = false;       // Send weekly summary (DISABLED FOR TESTING)

input group "=== Trade Management ==="
input bool   InpUseBreakeven = true;               // Enable Breakeven
input double InpBreakevenPips = 20.0;              // Pips in profit to move to breakeven
input double InpBreakevenBufferPips = 2.0;         // Buffer in pips for breakeven SL
input bool   InpUseTrailingStop = true;            // Enable Trailing Stop
input double InpTrailStartPips = 30.0;             // Pips in profit to activate trailing SL
input double InpTrailDistanceATRMultiplier = 2.0;  // Trailing distance in ATR multiples

input string InpSoundFileEntry = "alert.wav";      // Sound file for entries
input string InpSoundFileExit = "alert2.wav";      // Sound file for exits
input string InpSoundFileAlert = "timeout.wav";    // Sound file for alerts

//--- Global variables
ulong magicNumber = 123456;

CTrade trade;
//--- Current timeframe indicator handles
int ema20_handle;
int ema50_handle;
int ema200_handle;
int rsi_handle;
int macd_handle;
int atr_handle;

//--- Higher timeframe indicator handles
int ema20_htf1_handle;
int ema50_htf1_handle;
int ema200_htf1_handle;
int rsi_htf1_handle;
int macd_htf1_handle;

int ema20_htf2_handle;
int ema50_htf2_handle;
int ema200_htf2_handle;
int rsi_htf2_handle;

double dailyStartBalance;
double weeklyStartBalance;
double monthlyStartBalance;
int consecutiveLosses;
int dailyTrades;
int weeklyTrades;
datetime dayStart;
datetime weekStart; // Will be used for calendar week check
datetime monthStart;

//--- Debug counters
int debugSignalChecks = 0;
int debugTradesAttempted = 0;
int debugTradesExecuted = 0;
int debugMTFFailures = 0;
int debugEntryFailures = 0;

//--- Position State Tracking for Breakeven
struct PositionState
{
   ulong   ticket;
   bool    breakeven_applied;
};

PositionState managedPositions[];

//--- Multi-timeframe analysis structure
struct MTFAnalysis
{
    bool currentTF_bullish;
    bool currentTF_bearish;
    bool htf1_bullish;
    bool htf1_bearish;
    bool htf2_bullish;
    bool htf2_bearish;
    double trendStrength;
    bool allAligned;
    string analysis;
    string debugInfo;
};

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    trade.SetExpertMagicNumber(magicNumber);
    trade.SetTypeFillingBySymbol(_Symbol);

    //--- Initialize current timeframe indicators
    ema20_handle = iMA(_Symbol, PERIOD_CURRENT, InpEMA20Period, 0, MODE_EMA, PRICE_CLOSE);
    ema50_handle = iMA(_Symbol, PERIOD_CURRENT, InpEMA50Period, 0, MODE_EMA, PRICE_CLOSE);
    ema200_handle = iMA(_Symbol, PERIOD_CURRENT, InpEMA200Period, 0, MODE_EMA, PRICE_CLOSE);
    rsi_handle = iRSI(_Symbol, PERIOD_CURRENT, InpRSIPeriod, PRICE_CLOSE);
    macd_handle = iMACD(_Symbol, PERIOD_CURRENT, InpMACDFast, InpMACDSlow, InpMACDSignal, PRICE_CLOSE);
    atr_handle = iATR(_Symbol, PERIOD_CURRENT, InpATRPeriod);
    
    //--- Initialize higher timeframe 1 indicators
    ema20_htf1_handle = iMA(_Symbol, InpHigherTimeframe1, InpEMA20Period, 0, MODE_EMA, PRICE_CLOSE);
    ema50_htf1_handle = iMA(_Symbol, InpHigherTimeframe1, InpEMA50Period, 0, MODE_EMA, PRICE_CLOSE);
    ema200_htf1_handle = iMA(_Symbol, InpHigherTimeframe1, InpEMA200Period, 0, MODE_EMA, PRICE_CLOSE);
    rsi_htf1_handle = iRSI(_Symbol, InpHigherTimeframe1, InpRSIPeriod, PRICE_CLOSE);
    macd_htf1_handle = iMACD(_Symbol, InpHigherTimeframe1, InpMACDFast, InpMACDSlow, InpMACDSignal, PRICE_CLOSE);
    
    //--- Initialize higher timeframe 2 indicators
    ema20_htf2_handle = iMA(_Symbol, InpHigherTimeframe2, InpEMA20Period, 0, MODE_EMA, PRICE_CLOSE);
    ema50_htf2_handle = iMA(_Symbol, InpHigherTimeframe2, InpEMA50Period, 0, MODE_EMA, PRICE_CLOSE);
    ema200_htf2_handle = iMA(_Symbol, InpHigherTimeframe2, InpEMA200Period, 0, MODE_EMA, PRICE_CLOSE);
    rsi_htf2_handle = iRSI(_Symbol, InpHigherTimeframe2, InpRSIPeriod, PRICE_CLOSE);
    
    //--- Validate all handles
    if(ema20_handle == INVALID_HANDLE || ema50_handle == INVALID_HANDLE || 
       ema200_handle == INVALID_HANDLE || rsi_handle == INVALID_HANDLE || 
       macd_handle == INVALID_HANDLE || atr_handle == INVALID_HANDLE || ema20_htf1_handle == INVALID_HANDLE ||
       ema50_htf1_handle == INVALID_HANDLE || ema200_htf1_handle == INVALID_HANDLE ||
       rsi_htf1_handle == INVALID_HANDLE || macd_htf1_handle == INVALID_HANDLE ||
       ema20_htf2_handle == INVALID_HANDLE || ema50_htf2_handle == INVALID_HANDLE ||
       ema200_htf2_handle == INVALID_HANDLE || rsi_htf2_handle == INVALID_HANDLE)
    {
        Print("ERROR: Failed to create indicators");
        return INIT_FAILED;
    }
    
    //--- Initialize risk management variables
    InitializeRiskManagement();
    
    string initMessage = StringFormat("BACKTEST VERSION: ForexSwingTradingEA initialized for testing\nCurrent TF: %s, Higher TF1: %s, Higher TF2: %s\nRelaxed Mode: %s, MTF Confirmation: %s", 
                                    EnumToString(PERIOD_CURRENT), 
                                    EnumToString(InpHigherTimeframe1), 
                                    EnumToString(InpHigherTimeframe2),
                                    (InpRelaxedMode ? "ENABLED" : "DISABLED"),
                                    (InpUseMTFConfirmation ? "ENABLED" : "DISABLED"));
    
    Print("=== BACKTEST FIXED VERSION INITIALIZED ===");
    Print(initMessage);
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Print debug statistics
    Print("=== DEBUG STATISTICS ===");
    Print("Signal Checks: ", debugSignalChecks);
    Print("Trades Attempted: ", debugTradesAttempted);
    Print("Trades Executed: ", debugTradesExecuted);
    Print("MTF Failures: ", debugMTFFailures);
    Print("Entry Failures: ", debugEntryFailures);
    
    //--- Release indicator handles
    if(ema20_handle != INVALID_HANDLE) IndicatorRelease(ema20_handle);
    if(ema50_handle != INVALID_HANDLE) IndicatorRelease(ema50_handle);
    if(ema200_handle != INVALID_HANDLE) IndicatorRelease(ema200_handle);
    if(rsi_handle != INVALID_HANDLE) IndicatorRelease(rsi_handle);
    if(macd_handle != INVALID_HANDLE) IndicatorRelease(macd_handle);
    if(atr_handle != INVALID_HANDLE) IndicatorRelease(atr_handle);
    
    if(ema20_htf1_handle != INVALID_HANDLE) IndicatorRelease(ema20_htf1_handle);
    if(ema50_htf1_handle != INVALID_HANDLE) IndicatorRelease(ema50_htf1_handle);
    if(ema200_htf1_handle != INVALID_HANDLE) IndicatorRelease(ema200_htf1_handle);
    if(rsi_htf1_handle != INVALID_HANDLE) IndicatorRelease(rsi_htf1_handle);
    if(macd_htf1_handle != INVALID_HANDLE) IndicatorRelease(macd_htf1_handle);
    
    if(ema20_htf2_handle != INVALID_HANDLE) IndicatorRelease(ema20_htf2_handle);
    if(ema50_htf2_handle != INVALID_HANDLE) IndicatorRelease(ema50_htf2_handle);
    if(ema200_htf2_handle != INVALID_HANDLE) IndicatorRelease(ema200_htf2_handle);
    if(rsi_htf2_handle != INVALID_HANDLE) IndicatorRelease(rsi_htf2_handle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check if new bar
    static datetime lastBar = 0;
    if(iTime(_Symbol, PERIOD_CURRENT, 0) == lastBar)
        { ManagePositions(); return; } // Manage positions on every tick
    lastBar = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    debugSignalChecks++;
    
    //--- Update risk management
    UpdateRiskManagement();
    
    //--- Check trading conditions
    if(!IsTradingAllowed())
    {
        if(InpEnableDebugMode)
            Print("DEBUG: Trading not allowed at ", TimeToString(TimeCurrent()));
        return;
    }
    
    //--- Look for new trading opportunities
    CheckForTradingSignals();
}

//+------------------------------------------------------------------+
//| Initialize risk management variables                             |
//+------------------------------------------------------------------+
void InitializeRiskManagement()
{
    dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    weeklyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    monthlyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    consecutiveLosses = 0;
    dailyTrades = 0;
    weeklyTrades = 0;
    
    MqlDateTime dt; TimeToStruct(TimeCurrent(), dt);
    dayStart = StructToTime(dt);
    weekStart = StructToTime(dt);
    monthStart = StructToTime(dt);
}

//+------------------------------------------------------------------+
//| Update risk management variables                                 |
//+------------------------------------------------------------------+
void UpdateRiskManagement()
{
    datetime current_time = TimeCurrent();
    MqlDateTime dt_current; TimeToStruct(current_time, dt_current);
    MqlDateTime dt_day_start; TimeToStruct(dayStart, dt_day_start);
    MqlDateTime dt_week_start; TimeToStruct(weekStart, dt_week_start);
    MqlDateTime dt_month_start; TimeToStruct(monthStart, dt_month_start);
    
    //--- Reset daily counters
    if(dt_current.day_of_year != dt_day_start.day_of_year || dt_current.year != dt_day_start.year)
    {
        dailyTrades = 0;
        dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        dayStart = current_time;
    }
    
    //--- Reset weekly counters
    if(dt_current.day_of_week < dt_week_start.day_of_week || (current_time - weekStart) >= 7 * 24 * 3600)
    {
        weeklyTrades = 0;
        weeklyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        weekStart = current_time;
    }
    
    //--- Reset monthly counters
    if(dt_current.mon != dt_month_start.mon || dt_current.year != dt_month_start.year)
    {
        monthlyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        monthStart = current_time;
    }
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                      |
//+------------------------------------------------------------------+
bool IsTradingAllowed()
{
    //--- Check trading hours
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    if(dt.hour < InpStartHour || dt.hour >= InpEndHour)
        return false;
    
    //--- Check Friday trading
    if(!InpTradeOnFriday && dt.day_of_week == 5)
        return false;
    
    //--- Check daily loss limit (relaxed for testing)
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double dailyLoss = (dailyStartBalance - currentBalance) / dailyStartBalance * 100;
    if(dailyLoss >= InpMaxDailyLoss)
    {
        if(InpEnableDebugMode)
            Print("DEBUG: Daily loss limit reached: ", dailyLoss, "%");
        return false;
    }
    
    //--- Check consecutive losses (relaxed for testing)
    if(consecutiveLosses >= InpMaxConsecutiveLosses)
    {
        if(InpEnableDebugMode)
            Print("DEBUG: Maximum consecutive losses reached: ", consecutiveLosses);
        return false;
    }
    
    //--- Check daily trade limit (relaxed for testing)
    if(dailyTrades >= InpMaxTradesPerDay)
    {
        if(InpEnableDebugMode)
            Print("DEBUG: Daily trade limit reached: ", dailyTrades);
        return false;
    }
    
    //--- Check weekly trade limit (relaxed for testing)
    if(weeklyTrades >= InpMaxTradesPerWeek)
    {
        if(InpEnableDebugMode)
            Print("DEBUG: Weekly trade limit reached: ", weeklyTrades);
        return false;
    }
    
    //--- Skip news filter in relaxed mode
    if(!InpRelaxedMode && InpUseNewsFilter && IsNewsTime())
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Simplified news time check                                       |
//+------------------------------------------------------------------+
bool IsNewsTime()
{
    // Simplified - assume no news during testing
    return false;
}

//+------------------------------------------------------------------+
//| Check for trading signals with relaxed conditions               |
//+------------------------------------------------------------------+
void CheckForTradingSignals()
{
    //--- Get multi-timeframe analysis
    MTFAnalysis mtf = AnalyzeMultiTimeframe();
    
    if(InpEnableDebugMode && InpShowMTFInfo)
    {
        static datetime lastMTFPrint = 0;
        if(TimeCurrent() - lastMTFPrint > 3600) // Print every hour
        {
            Print("MTF Analysis: ", mtf.analysis);
            Print("Debug Info: ", mtf.debugInfo);
            lastMTFPrint = TimeCurrent();
        }
    }
    
    //--- Skip if MTF confirmation is required but not met
    if(InpUseMTFConfirmation)
    {
        if(!mtf.allAligned || mtf.trendStrength < InpMTFTrendStrength)
        {
            debugMTFFailures++;
            if(InpEnableDebugMode)
                Print("DEBUG: MTF confirmation failed - Aligned: ", mtf.allAligned, ", Strength: ", mtf.trendStrength);
            return;
        }
    }
    
    //--- Get current timeframe indicator values
    double ema20_buffer[], ema50_buffer[], ema200_buffer[];
    double rsi_buffer[], macd_main[], macd_signal[];
    
    ArraySetAsSeries(ema20_buffer, true);
    ArraySetAsSeries(ema50_buffer, true);
    ArraySetAsSeries(ema200_buffer, true);
    ArraySetAsSeries(rsi_buffer, true);
    ArraySetAsSeries(macd_main, true);
    ArraySetAsSeries(macd_signal, true);
    
    if(CopyBuffer(ema20_handle, 0, 0, 3, ema20_buffer) < 3 ||
       CopyBuffer(ema50_handle, 0, 0, 3, ema50_buffer) < 3 ||
       CopyBuffer(ema200_handle, 0, 0, 3, ema200_buffer) < 3 ||
       CopyBuffer(rsi_handle, 0, 0, 3, rsi_buffer) < 3 ||
       CopyBuffer(macd_handle, 0, 0, 3, macd_main) < 3 ||
       CopyBuffer(macd_handle, 1, 0, 3, macd_signal) < 3)
    {
        Print("ERROR: Failed to copy indicator buffers");
        return;
    }
    
    //--- Get current price
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    //--- Check for bullish signal
    bool bullishSetup = false;
    bool bearishSetup = false;
    string entryDebug = "";
    
    if(InpRelaxedMode)
    {
        bullishSetup = IsRelaxedBullishSetup(currentPrice, ema20_buffer, ema50_buffer, ema200_buffer, rsi_buffer, macd_main, macd_signal, entryDebug);
        bearishSetup = IsRelaxedBearishSetup(currentPrice, ema20_buffer, ema50_buffer, ema200_buffer, rsi_buffer, macd_main, macd_signal, entryDebug);
    }
    else
    {
        bullishSetup = IsBullishSetup(currentPrice, ema20_buffer, ema50_buffer, ema200_buffer, rsi_buffer, macd_main, macd_signal, entryDebug);
        bearishSetup = IsBearishSetup(currentPrice, ema20_buffer, ema50_buffer, ema200_buffer, rsi_buffer, macd_main, macd_signal, entryDebug);
    }
    
    if(InpEnableDebugMode && InpShowEntryConditions)
    {
        static datetime lastEntryPrint = 0;
        if(TimeCurrent() - lastEntryPrint > 1800) // Print every 30 minutes
        {
            Print("Entry Conditions Debug: ", entryDebug);
            Print("Bullish Setup: ", bullishSetup, ", Bearish Setup: ", bearishSetup);
            lastEntryPrint = TimeCurrent();
        }
    }
    
    //--- Execute trades with relaxed MTF requirements
    if(bullishSetup && (!InpUseMTFConfirmation || mtf.currentTF_bullish))
    {
        debugTradesAttempted++;
        OpenBuyTrade(mtf);
    }
    else if(bearishSetup && (!InpUseMTFConfirmation || mtf.currentTF_bearish))
    {
        debugTradesAttempted++;
        OpenSellTrade(mtf);
    }
    else
    {
        debugEntryFailures++;
    }
}

//+------------------------------------------------------------------+
//| Relaxed bullish setup conditions                                |
//+------------------------------------------------------------------+
bool IsRelaxedBullishSetup(double price, double &ema20[], double &ema50[], double &ema200[], double &rsi[], double &macd_main[], double &macd_signal[], string &debug)
{
    debug = "RELAXED BULLISH CHECK: ";
    
    //--- Relaxed trend filter (only need price above EMA50)
    bool trendOK = price > ema50[0];
    debug += StringFormat("Price>EMA50: %s, ", (trendOK ? "OK" : "FAIL"));
    
    //--- Relaxed EMA alignment (only need EMA20 > EMA50)
    bool emaAlignOK = ema20[0] > ema50[0];
    debug += StringFormat("EMA20>EMA50: %s, ", (emaAlignOK ? "OK" : "FAIL"));
    
    //--- Relaxed RSI conditions (broader range)
    bool rsiOK = rsi[0] > 40 && rsi[0] < 80;
    debug += StringFormat("RSI(40-80): %s (%.1f), ", (rsiOK ? "OK" : "FAIL"), rsi[0]);
    
    //--- Relaxed MACD conditions (just need MACD above signal OR rising)
    bool macdOK = (macd_main[0] > macd_signal[0]) || (macd_main[0] > macd_main[1]);
    debug += StringFormat("MACD: %s, ", (macdOK ? "OK" : "FAIL"));
    
    //--- Price momentum (relaxed)
    bool momentumOK = price > ema20[1]; // Just need price above previous EMA20
    debug += StringFormat("Momentum: %s", (momentumOK ? "OK" : "FAIL"));
    
    // Need at least 3 out of 5 conditions
    int conditions = (trendOK ? 1 : 0) + (emaAlignOK ? 1 : 0) + (rsiOK ? 1 : 0) + (macdOK ? 1 : 0) + (momentumOK ? 1 : 0);
    bool result = conditions >= 3;
    
    debug += StringFormat(" => %d/5 conditions met, Result: %s", conditions, (result ? "BUY SIGNAL" : "NO SIGNAL"));
    
    return result;
}

//+------------------------------------------------------------------+
//| Relaxed bearish setup conditions                                |
//+------------------------------------------------------------------+
bool IsRelaxedBearishSetup(double price, double &ema20[], double &ema50[], double &ema200[], double &rsi[], double &macd_main[], double &macd_signal[], string &debug)
{
    debug = "RELAXED BEARISH CHECK: ";
    
    //--- Relaxed trend filter (only need price below EMA50)
    bool trendOK = price < ema50[0];
    debug += StringFormat("Price<EMA50: %s, ", (trendOK ? "OK" : "FAIL"));
    
    //--- Relaxed EMA alignment (only need EMA20 < EMA50)
    bool emaAlignOK = ema20[0] < ema50[0];
    debug += StringFormat("EMA20<EMA50: %s, ", (emaAlignOK ? "OK" : "FAIL"));
    
    //--- Relaxed RSI conditions (broader range)
    bool rsiOK = rsi[0] < 60 && rsi[0] > 20;
    debug += StringFormat("RSI(20-60): %s (%.1f), ", (rsiOK ? "OK" : "FAIL"), rsi[0]);
    
    //--- Relaxed MACD conditions (just need MACD below signal OR falling)
    bool macdOK = (macd_main[0] < macd_signal[0]) || (macd_main[0] < macd_main[1]);
    debug += StringFormat("MACD: %s, ", (macdOK ? "OK" : "FAIL"));
    
    //--- Price momentum (relaxed)
    bool momentumOK = price < ema20[1]; // Just need price below previous EMA20
    debug += StringFormat("Momentum: %s", (momentumOK ? "OK" : "FAIL"));
    
    // Need at least 3 out of 5 conditions
    int conditions = (trendOK ? 1 : 0) + (emaAlignOK ? 1 : 0) + (rsiOK ? 1 : 0) + (macdOK ? 1 : 0) + (momentumOK ? 1 : 0);
    bool result = conditions >= 3;
    
    debug += StringFormat(" => %d/5 conditions met, Result: %s", conditions, (result ? "SELL SIGNAL" : "NO SIGNAL"));
    
    return result;
}

//+------------------------------------------------------------------+
//| Original bullish setup conditions                               |
//+------------------------------------------------------------------+
bool IsBullishSetup(double price, double &ema20[], double &ema50[], double &ema200[], double &rsi[], double &macd_main[], double &macd_signal[], string &debug)
{
    debug = "STRICT BULLISH CHECK: ";
    
    //--- Primary trend filter
    if(price <= ema200[0]) 
    {
        debug += "FAIL: Price <= EMA200";
        return false;
    }
    
    //--- EMA alignment
    if(ema20[0] <= ema50[0]) 
    {
        debug += "FAIL: EMA20 <= EMA50";
        return false;
    }
    
    //--- RSI conditions
    if(rsi[0] <= 30 || rsi[0] >= 70) 
    {
        debug += StringFormat("FAIL: RSI out of range (%.1f)", rsi[0]);
        return false;
    }
    if(rsi[1] <= rsi[0]) 
    {
        debug += "FAIL: RSI not rising";
        return false;
    }
    
    //--- MACD conditions
    if(macd_main[1] >= macd_signal[1]) 
    {
        debug += "FAIL: MACD was not below signal";
        return false;
    }
    if(macd_main[0] <= macd_signal[0]) 
    {
        debug += "FAIL: MACD did not cross above signal";
        return false;
    }
    
    //--- Price above EMA20 and EMA20 rising
    if(price <= ema20[0]) 
    {
        debug += "FAIL: Price <= EMA20";
        return false;
    }
    if(ema20[0] <= ema20[1]) 
    {
        debug += "FAIL: EMA20 not rising";
        return false;
    }
    
    debug += "SUCCESS: All conditions met";
    return true;
}

//+------------------------------------------------------------------+
//| Original bearish setup conditions                               |
//+------------------------------------------------------------------+
bool IsBearishSetup(double price, double &ema20[], double &ema50[], double &ema200[], double &rsi[], double &macd_main[], double &macd_signal[], string &debug)
{
    debug = "STRICT BEARISH CHECK: ";
    
    //--- Primary trend filter
    if(price >= ema200[0]) 
    {
        debug += "FAIL: Price >= EMA200";
        return false;
    }
    
    //--- EMA alignment
    if(ema20[0] >= ema50[0]) 
    {
        debug += "FAIL: EMA20 >= EMA50";
        return false;
    }
    
    //--- RSI conditions
    if(rsi[0] <= 30 || rsi[0] >= 70) 
    {
        debug += StringFormat("FAIL: RSI out of range (%.1f)", rsi[0]);
        return false;
    }
    if(rsi[1] >= rsi[0]) 
    {
        debug += "FAIL: RSI not falling";
        return false;
    }
    
    //--- MACD conditions
    if(macd_main[1] <= macd_signal[1]) 
    {
        debug += "FAIL: MACD was not above signal";
        return false;
    }
    if(macd_main[0] >= macd_signal[0]) 
    {
        debug += "FAIL: MACD did not cross below signal";
        return false;
    }
    
    //--- Price below EMA20 and EMA20 falling
    if(price >= ema20[0]) 
    {
        debug += "FAIL: Price >= EMA20";
        return false;
    }
    if(ema20[0] >= ema20[1]) 
    {
        debug += "FAIL: EMA20 not falling";
        return false;
    }
    
    debug += "SUCCESS: All conditions met";
    return true;
}

//+------------------------------------------------------------------+
//| Analyze multi-timeframe with relaxed conditions                 |
//+------------------------------------------------------------------+
MTFAnalysis AnalyzeMultiTimeframe()
{
    MTFAnalysis mtf;
    ZeroMemory(mtf);
    
    //--- Current timeframe analysis
    mtf.currentTF_bullish = AnalyzeTrendDirection(ema20_handle, ema50_handle, ema200_handle, rsi_handle, true);
    mtf.currentTF_bearish = AnalyzeTrendDirection(ema20_handle, ema50_handle, ema200_handle, rsi_handle, false);
    
    //--- Higher timeframe 1 analysis (relaxed for testing)
    mtf.htf1_bullish = InpRelaxedMode ? AnalyzeRelaxedTrendDirection(ema20_htf1_handle, ema50_htf1_handle, ema200_htf1_handle, rsi_htf1_handle, true) :
                       AnalyzeTrendDirection(ema20_htf1_handle, ema50_htf1_handle, ema200_htf1_handle, rsi_htf1_handle, true);
    mtf.htf1_bearish = InpRelaxedMode ? AnalyzeRelaxedTrendDirection(ema20_htf1_handle, ema50_htf1_handle, ema200_htf1_handle, rsi_htf1_handle, false) :
                       AnalyzeTrendDirection(ema20_htf1_handle, ema50_htf1_handle, ema200_htf1_handle, rsi_htf1_handle, false);
    
    //--- Higher timeframe 2 analysis (relaxed for testing)
    mtf.htf2_bullish = InpRelaxedMode ? AnalyzeRelaxedTrendDirection(ema20_htf2_handle, ema50_htf2_handle, ema200_htf2_handle, rsi_htf2_handle, true) :
                       AnalyzeTrendDirection(ema20_htf2_handle, ema50_htf2_handle, ema200_htf2_handle, rsi_htf2_handle, true);
    mtf.htf2_bearish = InpRelaxedMode ? AnalyzeRelaxedTrendDirection(ema20_htf2_handle, ema50_htf2_handle, ema200_htf2_handle, rsi_htf2_handle, false) :
                       AnalyzeTrendDirection(ema20_htf2_handle, ema50_htf2_handle, ema200_htf2_handle, rsi_htf2_handle, false);
    
    //--- Calculate trend strength
    int bullish_count = (mtf.currentTF_bullish ? 1 : 0) + (mtf.htf1_bullish ? 1 : 0) + (mtf.htf2_bullish ? 1 : 0);
    int bearish_count = (mtf.currentTF_bearish ? 1 : 0) + (mtf.htf1_bearish ? 1 : 0) + (mtf.htf2_bearish ? 1 : 0);
    
    if(bullish_count > bearish_count)
        mtf.trendStrength = (double)bullish_count / 3.0 * 100.0;
    else if(bearish_count > bullish_count)
        mtf.trendStrength = (double)bearish_count / 3.0 * 100.0;
    else
        mtf.trendStrength = 0; // Neutral/conflicting signals
    
    //--- Check alignment (relaxed for testing)
    if(InpRequireAllTFAlignment)
    {
        mtf.allAligned = (bullish_count == 3) || (bearish_count == 3);
    }
    else
    {
        // More relaxed alignment - just need current timeframe agreement
        mtf.allAligned = (bullish_count >= 1) || (bearish_count >= 1);
    }
    
    //--- Create analysis string
    mtf.analysis = StringFormat("Current=%s, %s=%s, %s=%s, Strength=%.0f%%, Aligned=%s",
                               (mtf.currentTF_bullish ? "BULL" : (mtf.currentTF_bearish ? "BEAR" : "NEUTRAL")),
                               EnumToString(InpHigherTimeframe1),
                               (mtf.htf1_bullish ? "BULL" : (mtf.htf1_bearish ? "BEAR" : "NEUTRAL")),
                               EnumToString(InpHigherTimeframe2),
                               (mtf.htf2_bullish ? "BULL" : (mtf.htf2_bearish ? "BEAR" : "NEUTRAL")),
                               mtf.trendStrength,
                               (mtf.allAligned ? "YES" : "NO"));
    
    mtf.debugInfo = StringFormat("Bullish_count=%d, Bearish_count=%d, Mode=%s", 
                                bullish_count, bearish_count, 
                                (InpRelaxedMode ? "RELAXED" : "STRICT"));
    
    return mtf;
}

//+------------------------------------------------------------------+
//| Analyze trend direction with original strict conditions         |
//+------------------------------------------------------------------+
bool AnalyzeTrendDirection(int ema20_h, int ema50_h, int ema200_h, int rsi_h, bool checkBullish)
{
    double ema20[], ema50[], ema200[], rsi[];
    ArraySetAsSeries(ema20, true);
    ArraySetAsSeries(ema50, true);
    ArraySetAsSeries(ema200, true);
    ArraySetAsSeries(rsi, true);
    
    if(CopyBuffer(ema20_h, 0, 0, 3, ema20) < 3 ||
       CopyBuffer(ema50_h, 0, 0, 3, ema50) < 3 ||
       CopyBuffer(ema200_h, 0, 0, 3, ema200) < 3 ||
       CopyBuffer(rsi_h, 0, 0, 3, rsi) < 3)
        return false;
    
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    if(checkBullish)
    {
        // Strict bullish trend conditions
        if(currentPrice > ema200[0] &&           // Price above 200 EMA
           ema20[0] > ema50[0] &&                // 20 EMA above 50 EMA
           ema50[0] > ema200[0] &&               // 50 EMA above 200 EMA
           ema20[0] > ema20[1] &&                // 20 EMA rising
           rsi[0] > 50)                          // RSI above 50
            return true;
    }
    else
    {
        // Strict bearish trend conditions
        if(currentPrice < ema200[0] &&           // Price below 200 EMA
           ema20[0] < ema50[0] &&                // 20 EMA below 50 EMA
           ema50[0] < ema200[0] &&               // 50 EMA below 200 EMA
           ema20[0] < ema20[1] &&                // 20 EMA falling
           rsi[0] < 50)                          // RSI below 50
            return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Analyze trend direction with relaxed conditions                 |
//+------------------------------------------------------------------+
bool AnalyzeRelaxedTrendDirection(int ema20_h, int ema50_h, int ema200_h, int rsi_h, bool checkBullish)
{
    double ema20[], ema50[], ema200[], rsi[];
    ArraySetAsSeries(ema20, true);
    ArraySetAsSeries(ema50, true);
    ArraySetAsSeries(ema200, true);
    ArraySetAsSeries(rsi, true);
    
    if(CopyBuffer(ema20_h, 0, 0, 3, ema20) < 3 ||
       CopyBuffer(ema50_h, 0, 0, 3, ema50) < 3 ||
       CopyBuffer(ema200_h, 0, 0, 3, ema200) < 3 ||
       CopyBuffer(rsi_h, 0, 0, 3, rsi) < 3)
        return false;
    
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    if(checkBullish)
    {
        // Relaxed bullish trend conditions - need 2 out of 3
        int conditions = 0;
        if(currentPrice > ema50[0]) conditions++;        // Price above 50 EMA (relaxed from 200)
        if(ema20[0] > ema50[0]) conditions++;            // 20 EMA above 50 EMA
        if(rsi[0] > 45) conditions++;                    // RSI above 45 (relaxed from 50)
        
        return conditions >= 2;
    }
    else
    {
        // Relaxed bearish trend conditions - need 2 out of 3
        int conditions = 0;
        if(currentPrice < ema50[0]) conditions++;        // Price below 50 EMA (relaxed from 200)
        if(ema20[0] < ema50[0]) conditions++;            // 20 EMA below 50 EMA
        if(rsi[0] < 55) conditions++;                    // RSI below 55 (relaxed from 50)
        
        return conditions >= 2;
    }
}

//+------------------------------------------------------------------+
//| Open buy trade                                                   |
//+------------------------------------------------------------------+
void OpenBuyTrade(MTFAnalysis &mtf)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = CalculateStopLoss(ORDER_TYPE_BUY, ask);
    double tp = CalculateTakeProfit(ORDER_TYPE_BUY, ask, sl);
    double lots = CalculatePositionSize(ask, sl);
    
    if(lots > 0 && sl > 0 && tp > 0)
    {
        string comment = StringFormat("Swing Buy %s", (InpRelaxedMode ? "RELAXED" : "STRICT"));
        if(trade.Buy(lots, _Symbol, ask, sl, tp, comment))
        {
            debugTradesExecuted++;
            dailyTrades++;
            weeklyTrades++;
            
            // Add to managed positions
            PositionState newPos;
            newPos.ticket = trade.ResultDeal();
            newPos.breakeven_applied = false;
            int size = ArraySize(managedPositions);
            ArrayResize(managedPositions, size + 1);
            managedPositions[size] = newPos;
            
            if(InpEnableDebugMode)
            {
                Print("BUY TRADE EXECUTED: Ticket=", newPos.ticket, ", Price=", ask, ", SL=", sl, ", TP=", tp, ", Lots=", lots);
                Print("MTF Analysis: ", mtf.analysis);
            }
        }
        else
        {
            if(InpEnableDebugMode)
                Print("BUY TRADE FAILED: Error=", trade.ResultRetcode(), ", ", trade.ResultComment());
        }
    }
    else
    {
        if(InpEnableDebugMode)
            Print("BUY TRADE REJECTED: Invalid parameters - Lots=", lots, ", SL=", sl, ", TP=", tp);
    }
}

//+------------------------------------------------------------------+
//| Open sell trade                                                  |
//+------------------------------------------------------------------+
void OpenSellTrade(MTFAnalysis &mtf)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = CalculateStopLoss(ORDER_TYPE_SELL, bid);
    double tp = CalculateTakeProfit(ORDER_TYPE_SELL, bid, sl);
    double lots = CalculatePositionSize(bid, sl);
    
    if(lots > 0 && sl > 0 && tp > 0)
    {
        string comment = StringFormat("Swing Sell %s", (InpRelaxedMode ? "RELAXED" : "STRICT"));
        if(trade.Sell(lots, _Symbol, bid, sl, tp, comment))
        {
            debugTradesExecuted++;
            dailyTrades++;
            weeklyTrades++;
            
            // Add to managed positions
            PositionState newPos;
            newPos.ticket = trade.ResultDeal();
            newPos.breakeven_applied = false;
            int size = ArraySize(managedPositions);
            ArrayResize(managedPositions, size + 1);
            managedPositions[size] = newPos;
            
            if(InpEnableDebugMode)
            {
                Print("SELL TRADE EXECUTED: Ticket=", newPos.ticket, ", Price=", bid, ", SL=", sl, ", TP=", tp, ", Lots=", lots);
                Print("MTF Analysis: ", mtf.analysis);
            }
        }
        else
        {
            if(InpEnableDebugMode)
                Print("SELL TRADE FAILED: Error=", trade.ResultRetcode(), ", ", trade.ResultComment());
        }
    }
    else
    {
        if(InpEnableDebugMode)
            Print("SELL TRADE REJECTED: Invalid parameters - Lots=", lots, ", SL=", sl, ", TP=", tp);
    }
}

//+------------------------------------------------------------------+
//| Calculate stop loss                                              |
//+------------------------------------------------------------------+
double CalculateStopLoss(ENUM_ORDER_TYPE orderType, double entryPrice)
{
    double atr[];
    ArraySetAsSeries(atr, true);

    // Use the global ATR handle created in OnInit    
    if(atr_handle == INVALID_HANDLE) return 0;
    
    if(CopyBuffer(atr_handle, 0, 0, 1, atr) < 1)
    {
        return 0;
    }
    
    double atrValue = atr[0];
    double slDistance = atrValue * InpStopLossATRMultiplier;
    
    if(orderType == ORDER_TYPE_BUY)
        return entryPrice - slDistance;
    else
        return entryPrice + slDistance;
}

//+------------------------------------------------------------------+
//| Calculate take profit                                            |
//+------------------------------------------------------------------+
double CalculateTakeProfit(ENUM_ORDER_TYPE orderType, double entryPrice, double stopLoss)
{
    double slDistance = MathAbs(entryPrice - stopLoss);
    double tpDistance = slDistance * InpTargetRiskReward;
    
    if(orderType == ORDER_TYPE_BUY)
        return entryPrice + tpDistance;
    else
        return entryPrice - tpDistance;
}

//+------------------------------------------------------------------+
//| Calculate position size                                          |
//+------------------------------------------------------------------+
double CalculatePositionSize(double entryPrice, double stopLoss)
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = balance * InpRiskPercentage / 100.0;
    double slDistance = MathAbs(entryPrice - stopLoss);
    
    if(slDistance <= 0) return 0;
    
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    if(tickValue <= 0 || tickSize <= 0) return 0;
    
    double lots = (riskAmount * tickSize) / (slDistance * tickValue);
    
    // Normalize lot size
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lots = MathMax(minLot, MathMin(maxLot, MathRound(lots / lotStep) * lotStep));
    
    return lots;
}

//+------------------------------------------------------------------+
//| Manage existing positions                                        |
//+------------------------------------------------------------------+
void ManagePositions()
{
    if(!InpUseBreakeven && !InpUseTrailingStop) return;

    // Get ATR value once for all positions if trailing stop is enabled
    double current_atr = 0;
    if(InpUseTrailingStop)
    {
        double atr_buffer[];
        ArraySetAsSeries(atr_buffer, true);
        if(CopyBuffer(atr_handle, 0, 0, 1, atr_buffer) < 1) return;
        current_atr = atr_buffer[0];
    }

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetInteger(POSITION_MAGIC) != magicNumber || PositionGetString(POSITION_SYMBOL) != _Symbol) continue;

        // Find the corresponding managed state for this position
        int pos_idx = -1;
        for(int j = 0; j < ArraySize(managedPositions); j++)
        {
            if(managedPositions[j].ticket == ticket) { pos_idx = j; break; }
        }

        // If position is not tracked, add it now to be managed.
        // This makes the EA robust to restarts.
        if(pos_idx == -1)
        {
            PositionState newPos;
            newPos.ticket = ticket;
            newPos.breakeven_applied = false; // Assume not applied for existing trades
            int size = ArraySize(managedPositions);
            ArrayResize(managedPositions, size + 1);
            managedPositions[size] = newPos;
            pos_idx = size;
            if(InpEnableDebugMode) Print("DEBUG: Discovered and now managing existing position #", ticket);
        }

        if(pos_idx != -1)
        {
            // --- Common variables for BE and TSL ---
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_sl = PositionGetDouble(POSITION_SL);
            ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
            double current_price = SymbolInfoDouble(_Symbol, (type == POSITION_TYPE_BUY) ? SYMBOL_BID : SYMBOL_ASK);
            double profit_pips = (type == POSITION_TYPE_BUY) ? (current_price - open_price) / point / 10 : (open_price - current_price) / point / 10;

            // --- Breakeven Logic ---
            if (InpUseBreakeven && !managedPositions[pos_idx].breakeven_applied)
            {
                if (profit_pips >= InpBreakevenPips)
                {
                    double breakeven_sl = open_price + ((type == POSITION_TYPE_BUY ? 1 : -1) * InpBreakevenBufferPips * point * 10);
                    bool should_modify = (type == POSITION_TYPE_BUY && breakeven_sl > current_sl) || (type == POSITION_TYPE_SELL && breakeven_sl < current_sl);

                    if (should_modify)
                    {
                        if(trade.PositionModify(ticket, breakeven_sl, PositionGetDouble(POSITION_TP)))
                        {
                            managedPositions[pos_idx].breakeven_applied = true;
                            if(InpNotifyOnTrailingStop) Alert("Breakeven: Position #", ticket, " moved to breakeven."); // Simplified notification
                        }
                    }
                }
            }
            
            // --- Trailing Stop Logic ---
            if (InpUseTrailingStop && profit_pips >= InpTrailStartPips && current_atr > 0)
            {
                double new_sl = 0;
                if(type == POSITION_TYPE_BUY)
                {
                    new_sl = current_price - (current_atr * InpTrailDistanceATRMultiplier);
                }
                else // POSITION_TYPE_SELL
                {
                    new_sl = current_price + (current_atr * InpTrailDistanceATRMultiplier);
                }

                bool should_modify_tsl = (type == POSITION_TYPE_BUY && new_sl > current_sl) || (type == POSITION_TYPE_SELL && new_sl < current_sl);

                if (should_modify_tsl && trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP)))
                {
                    if(InpNotifyOnTrailingStop) 
                        Alert("Trailing Stop: Position #", ticket, " SL moved to ", DoubleToString(new_sl, _Digits));
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Count correlated positions (simplified)                         |
//+------------------------------------------------------------------+
int CountCorrelatedPositions()
{
    // Simplified - just count all positions for this EA
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == magicNumber)
            count++;
    }
    return count;
}