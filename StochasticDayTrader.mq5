//+------------------------------------------------------------------+
//|                                        StochasticDayTrader.mq5 |
//|                                  Copyright 2025, Your Name |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name"
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Include libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//--- EA Input Parameters
//--- Stochastic Settings
input int      Stoch_K_Period      = 8;   // Stochastic %K period
input int      Stoch_D_Period      = 3;   // Stochastic %D period
input int      Stoch_Slowing       = 3;   // Stochastic slowing
input int      Stoch_Overbought    = 80;  // Stochastic overbought level
input int      Stoch_Oversold      = 20;  // Stochastic oversold level

//--- ATR Settings
input int      ATR_Period          = 14;  // ATR period
input double   ATR_SL_Multiplier   = 2.0; // ATR multiplier for Stop Loss
input double   ATR_TP1_Multiplier  = 1.5; // ATR multiplier for Take Profit 1
input double   ATR_TP2_Multiplier  = 3.0; // ATR multiplier for Take Profit 2
input double   ATR_TP3_Multiplier  = 4.5; // ATR multiplier for Take Profit 3

//--- Trade Management
input double   LotSize             = 0.01; // Fixed lot size
input ulong    MagicNumber         = 12345; // EA's magic number
input int      Slippage            = 3;    // Slippage in points

//--- Trailing Stop
input bool     UseTrailingStop     = true; // Use trailing stop
input double   Trail_ATR_Multiplier = 1.5; // ATR multiplier for trailing stop

//--- Breakeven
input bool     UseBreakeven        = true; // Use breakeven
input int      Breakeven_Pips      = 1;   // Pips to add to breakeven price

//--- Basic Risk Management
input group    "=== DAILY LIMITS ==="
input bool     UseDailyLossLimit   = true;  // Enable daily loss limit
input double   DailyLossLimit      = 100.0; // Daily loss limit in account currency
input int      MaxDailyTrades      = 10;    // Maximum trades per day

input group    "=== DRAWDOWN PROTECTION ==="
input bool     UseDrawdownLimit    = true;  // Enable maximum drawdown protection
input double   MaxDrawdownPercent  = 15.0;  // Maximum drawdown % from account high
input double   MinAccountBalance   = 1000.0; // Minimum account balance to continue trading

input group    "=== POSITION LIMITS ==="
input int      MaxConcurrentTrades = 9;     // Maximum concurrent positions (3 sets of 3)
input bool     UsePositionLimits   = true;  // Enable position limits

input group    "=== TRADING HOURS ==="
input bool     UseTradingHours     = false; // Enable trading hours restriction
input int      StartHour           = 8;     // Trading start hour (server time)
input int      EndHour             = 18;    // Trading end hour (server time)

//--- Global variables
CTrade         trade;
CPositionInfo  posInfo;
int            stoch_handle;
int            atr_handle;

//--- Risk Management Variables
datetime       last_reset_date;        // Last date when daily stats were reset
double         daily_start_balance;    // Account balance at start of day
double         daily_profit_loss;      // Current daily P&L
int            daily_trade_count;      // Number of trades opened today
double         account_high_balance;   // Highest account balance reached
bool           trading_allowed;        // Master switch for trading

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
//---
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetDeviationInPoints(Slippage);
   trade.SetTypeFillingBySymbol(_Symbol);
   posInfo.Select(_Symbol);

//--- Create indicator handles
   stoch_handle = iStochastic(_Symbol, _Period, Stoch_K_Period, Stoch_D_Period, Stoch_Slowing, MODE_SMA, STO_LOWHIGH);
   if(stoch_handle == INVALID_HANDLE)
     {
      Print("Error creating Stochastic indicator handle");
      return(INIT_FAILED);
     }

   atr_handle = iATR(_Symbol, _Period, ATR_Period);
   if(atr_handle == INVALID_HANDLE)
     {
      Print("Error creating ATR indicator handle");
      return(INIT_FAILED);
     }

//--- Initialize risk management
   datetime current_time = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(current_time, dt);
   dt.hour = 0;
   dt.min = 0;
   dt.sec = 0;
   last_reset_date = StructToTime(dt);

   daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   account_high_balance = daily_start_balance;
   daily_profit_loss = 0.0;
   daily_trade_count = 0;
   trading_allowed = true;

   PrintFormat("Risk Management Initialized - Start Balance: %.2f", daily_start_balance);

//--- Print broker specifications for debugging
   PrintBrokerSpecs();

//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
//---
   IndicatorRelease(stoch_handle);
   IndicatorRelease(atr_handle);
  }

//+------------------------------------------------------------------+
//| Print broker specifications for debugging                        |
//+------------------------------------------------------------------+
void PrintBrokerSpecs()
  {
   long stops_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
   long freeze_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_FREEZE_LEVEL);
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);

   PrintFormat("=== Broker Specifications for %s ===", _Symbol);
   PrintFormat("Digits: %d", _Digits);
   PrintFormat("Point: %.5f", _Point);
   PrintFormat("Stops Level: %d points (%.5f)", stops_level, stops_level * _Point);
   PrintFormat("Freeze Level: %d points (%.5f)", freeze_level, freeze_level * _Point);
   PrintFormat("Min Lot: %.2f", min_lot);
   PrintFormat("Max Lot: %.2f", max_lot);
   PrintFormat("Lot Step: %.2f", lot_step);
   PrintFormat("Tick Size: %.5f", tick_size);
   PrintFormat("Tick Value: %.2f", tick_value);
   PrintFormat("=====================================");
  }

//+------------------------------------------------------------------+
//| Reset daily statistics at start of new trading day              |
//+------------------------------------------------------------------+
void ResetDailyStats()
  {
   datetime current_time = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(current_time, dt);

   // Check if it's a new day
   dt.hour = 0;
   dt.min = 0;
   dt.sec = 0;
   datetime today_start = StructToTime(dt);

   if(today_start > last_reset_date)
     {
      last_reset_date = today_start;
      daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
      daily_profit_loss = 0.0;
      daily_trade_count = 0;
      trading_allowed = true;

      // Update account high if balance increased
      double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
      if(current_balance > account_high_balance)
         account_high_balance = current_balance;

      PrintFormat("Daily stats reset - New day started. Balance: %.2f", daily_start_balance);
     }
  }

//+------------------------------------------------------------------+
//| Check if daily loss limit is exceeded                            |
//+------------------------------------------------------------------+
bool IsDailyLossLimitExceeded()
  {
   if(!UseDailyLossLimit)
      return false;

   double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double daily_loss = daily_start_balance - current_balance;

   if(daily_loss >= DailyLossLimit)
     {
      PrintFormat("Daily loss limit exceeded: %.2f >= %.2f", daily_loss, DailyLossLimit);
      return true;
     }

   return false;
  }

//+------------------------------------------------------------------+
//| Check if maximum drawdown limit is exceeded                      |
//+------------------------------------------------------------------+
bool IsDrawdownLimitExceeded()
  {
   if(!UseDrawdownLimit)
      return false;

   double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double drawdown_pct = ((account_high_balance - current_balance) / account_high_balance) * 100.0;

   if(drawdown_pct >= MaxDrawdownPercent)
     {
      PrintFormat("Maximum drawdown exceeded: %.2f%% >= %.2f%%", drawdown_pct, MaxDrawdownPercent);
      return true;
     }

   return false;
  }

//+------------------------------------------------------------------+
//| Check if position limits are exceeded                            |
//+------------------------------------------------------------------+
bool IsPositionLimitExceeded()
  {
   if(!UsePositionLimits)
      return false;

   int current_positions = 0;
   for(int i = 0; i < PositionsTotal(); i++)
     {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
         current_positions++;
     }

   if(current_positions >= MaxConcurrentTrades)
     {
      PrintFormat("Position limit exceeded: %d >= %d", current_positions, MaxConcurrentTrades);
      return true;
     }

   if(daily_trade_count >= MaxDailyTrades)
     {
      PrintFormat("Daily trade limit exceeded: %d >= %d", daily_trade_count, MaxDailyTrades);
      return true;
     }

   return false;
  }

//+------------------------------------------------------------------+
//| Master risk validation function                                  |
//+------------------------------------------------------------------+
bool IsRiskManagementValid()
  {
   // Reset daily stats if new day
   ResetDailyStats();

   // Update account high
   double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   if(current_balance > account_high_balance)
      account_high_balance = current_balance;

   // Check all risk parameters
   if(IsDailyLossLimitExceeded())
     {
      trading_allowed = false;
      return false;
     }

   if(IsDrawdownLimitExceeded())
     {
      trading_allowed = false;
      return false;
     }

   if(current_balance <= MinAccountBalance)
     {
      PrintFormat("Minimum account balance reached: %.2f <= %.2f", current_balance, MinAccountBalance);
      trading_allowed = false;
      return false;
     }

   if(IsPositionLimitExceeded())
     {
      return false;
     }

   // Check trading hours
   if(UseTradingHours)
     {
      MqlDateTime dt;
      TimeToStruct(TimeCurrent(), dt);
      int current_hour = dt.hour;

      if(StartHour <= EndHour)
        {
         if(current_hour < StartHour || current_hour >= EndHour)
            return false;
        }
      else
        {
         if(current_hour < StartHour && current_hour >= EndHour)
            return false;
        }
     }

   return true;
  }
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
  {
//---
   //--- Manage open positions on every tick
   ManageTrades();

   //--- Check for new bar before running trading logic
   if(!IsNewBar())
      return;

   //--- Trading logic
   CheckSignals();
  }
//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckSignals()
  {
//--- First check risk management
   if(!IsRiskManagementValid())
     {
      return;
     }

//--- No new trades if a position is already open
   if(posInfo.Select(_Symbol))
      if(posInfo.Magic() == MagicNumber)
         return;

//--- Get Stochastic values
   double stoch_main_buffer[], stoch_signal_buffer[];
   ArraySetAsSeries(stoch_main_buffer, true);
   ArraySetAsSeries(stoch_signal_buffer, true);

   if(CopyBuffer(stoch_handle, 0, 0, 3, stoch_main_buffer) < 3 || CopyBuffer(stoch_handle, 1, 0, 3, stoch_signal_buffer) < 3)
     {
      Print("Error copying stochastic buffers");
      return;
     }

   double stoch_main_1 = stoch_main_buffer[1];
   double stoch_signal_1 = stoch_signal_buffer[1];
   double stoch_main_2 = stoch_main_buffer[2];
   double stoch_signal_2 = stoch_signal_buffer[2];

//--- Buy Signal: Main line crosses above signal line in the oversold zone
   if(stoch_main_2 < stoch_signal_2 && stoch_main_1 > stoch_signal_1 && stoch_main_1 < Stoch_Oversold)
     {
      OpenTrade(ORDER_TYPE_BUY);
     }

//--- Sell Signal: Main line crosses below signal line in the overbought zone
   if(stoch_main_2 > stoch_signal_2 && stoch_main_1 < stoch_signal_1 && stoch_main_1 > Stoch_Overbought)
     {
      OpenTrade(ORDER_TYPE_SELL);
     }
  }
//+------------------------------------------------------------------+
//| Open a new trade                                                 |
//+------------------------------------------------------------------+
void OpenTrade(ENUM_ORDER_TYPE type)
  {
//--- Get current price and ATR
   double price = SymbolInfoDouble(_Symbol, type == ORDER_TYPE_BUY ? SYMBOL_ASK : SYMBOL_BID);
   double atr_buffer[];
   ArraySetAsSeries(atr_buffer, true);
   if(CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
     {
      Print("Error copying ATR buffer");
      return;
     }
   double atr = atr_buffer[1];
   double point = _Point;

//--- Get broker's minimum stop and freeze levels first
   long stops_level_long = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
   long freeze_level_long = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_FREEZE_LEVEL);
   double stops_level = stops_level_long * _Point;
   double freeze_level = freeze_level_long * _Point;

   // Use the larger of stops_level or freeze_level as minimum distance
   double min_distance = MathMax(stops_level, freeze_level);

   // If broker doesn't specify minimum, use reasonable default
   if(min_distance == 0)
     {
      min_distance = (_Digits == 5 || _Digits == 3) ? 10 * _Point : 1 * _Point;
     }

   PrintFormat("Debug: price=%.5f, atr=%.5f, stops=%d, freeze=%d, min_distance=%.5f",
               price, atr, stops_level_long, freeze_level_long, min_distance);

//--- Calculate ATR-based distances
   double atr_sl_distance = atr * ATR_SL_Multiplier;
   double atr_tp1_distance = atr * ATR_TP1_Multiplier;
   double atr_tp2_distance = atr * ATR_TP2_Multiplier;
   double atr_tp3_distance = atr * ATR_TP3_Multiplier;

//--- Ensure ATR distances meet minimum requirements
   double sl_distance = MathMax(atr_sl_distance, min_distance);
   double tp1_distance = MathMax(atr_tp1_distance, min_distance);
   double tp2_distance = MathMax(atr_tp2_distance, min_distance * 2);
   double tp3_distance = MathMax(atr_tp3_distance, min_distance * 3);

   PrintFormat("Distances: SL=%.5f, TP1=%.5f, TP2=%.5f, TP3=%.5f",
               sl_distance, tp1_distance, tp2_distance, tp3_distance);

//--- Calculate final SL and TPs using validated distances
   double sl, tp1, tp2, tp3;
   if(type == ORDER_TYPE_BUY)
     {
      sl = price - sl_distance;
      tp1 = price + tp1_distance;
      tp2 = price + tp2_distance;
      tp3 = price + tp3_distance;
     }
   else // ORDER_TYPE_SELL
     {
      sl = price + sl_distance;
      tp1 = price - tp1_distance;
      tp2 = price - tp2_distance;
      tp3 = price - tp3_distance;
     }

//--- Normalize prices
   sl = NormalizeDouble(sl, _Digits);
   tp1 = NormalizeDouble(tp1, _Digits);
   tp2 = NormalizeDouble(tp2, _Digits);
   tp3 = NormalizeDouble(tp3, _Digits);

//--- Final validation - ensure all values are positive and logical
   if(sl <= 0 || tp1 <= 0 || tp2 <= 0 || tp3 <= 0)
     {
      PrintFormat("Error: Invalid stop levels after adjustment - sl=%.5f, tp1=%.5f, tp2=%.5f, tp3=%.5f",
                  sl, tp1, tp2, tp3);
      return;
     }

//--- Validate order direction logic
   if(type == ORDER_TYPE_BUY)
     {
      if(sl >= price || tp1 <= price || tp2 <= price || tp3 <= price)
        {
         PrintFormat("Error: Invalid BUY levels - price=%.5f, sl=%.5f, tp1=%.5f, tp2=%.5f, tp3=%.5f",
                     price, sl, tp1, tp2, tp3);
         return;
        }
      // Ensure TPs are in ascending order
      if(tp1 >= tp2 || tp2 >= tp3)
        {
         PrintFormat("Error: BUY TPs not in ascending order - tp1=%.5f, tp2=%.5f, tp3=%.5f", tp1, tp2, tp3);
         return;
        }
     }
   else // ORDER_TYPE_SELL
     {
      if(sl <= price || tp1 >= price || tp2 >= price || tp3 >= price)
        {
         PrintFormat("Error: Invalid SELL levels - price=%.5f, sl=%.5f, tp1=%.5f, tp2=%.5f, tp3=%.5f",
                     price, sl, tp1, tp2, tp3);
         return;
        }
      // Ensure TPs are in descending order
      if(tp1 <= tp2 || tp2 <= tp3)
        {
         PrintFormat("Error: SELL TPs not in descending order - tp1=%.5f, tp2=%.5f, tp3=%.5f", tp1, tp2, tp3);
         return;
        }
     }

//--- Print values for debugging
   PrintFormat("Final Values: sl=%.5f, tp1=%.5f, tp2=%.5f, tp3=%.5f", sl, tp1, tp2, tp3);

//--- Open 3 partial positions
   double partial_lot = NormalizeDouble(LotSize / 3.0, 2);
   if(partial_lot < SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN))
      partial_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);

   trade.PositionOpen(_Symbol, type, partial_lot, price, sl, tp1, "TP1");
   trade.PositionOpen(_Symbol, type, partial_lot, price, sl, tp2, "TP2");
   trade.PositionOpen(_Symbol, type, partial_lot, price, sl, tp3, "TP3");

//--- Update trade count
   daily_trade_count++;
   PrintFormat("Trade opened. Daily count: %d/%d", daily_trade_count, MaxDailyTrades);
  }
//+------------------------------------------------------------------+
//| Manage open trades                                               |
//+------------------------------------------------------------------+
void ManageTrades()
  {
//--- Breakeven logic
   if(UseBreakeven && PositionsTotal() == 2)
     {
      for(int i = PositionsTotal() - 1; i >= 0; i--)
        {
         if(posInfo.SelectByIndex(i) && posInfo.Magic() == MagicNumber && posInfo.Symbol() == _Symbol)
           {
            double open_price = posInfo.PriceOpen();
            double sl = posInfo.StopLoss();
            ENUM_POSITION_TYPE type = posInfo.PositionType();

            if(type == POSITION_TYPE_BUY && sl < open_price)
              {
               trade.PositionModify(posInfo.Ticket(), open_price + Breakeven_Pips * _Point, posInfo.TakeProfit());
              }
            else if(type == POSITION_TYPE_SELL && sl > open_price)
              {
               trade.PositionModify(posInfo.Ticket(), open_price - Breakeven_Pips * _Point, posInfo.TakeProfit());
              }
           }
        }
     }

//--- Trailing stop logic
   if(UseTrailingStop)
     {
      double atr_buffer[];
      ArraySetAsSeries(atr_buffer, true);
      if(CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
        {
         Print("Error copying ATR buffer for trailing stop");
         return;
        }
      double atr = atr_buffer[1];

      for(int i = PositionsTotal() - 1; i >= 0; i--)
        {
         if(posInfo.SelectByIndex(i) && posInfo.Magic() == MagicNumber && posInfo.Symbol() == _Symbol)
           {
            double current_price = SymbolInfoDouble(_Symbol, posInfo.PositionType() == POSITION_TYPE_BUY ? SYMBOL_BID : SYMBOL_ASK);
            double open_price = posInfo.PriceOpen();
            double current_sl = posInfo.StopLoss();
            double new_sl;

            if(posInfo.PositionType() == POSITION_TYPE_BUY)
              {
               new_sl = current_price - atr * Trail_ATR_Multiplier;
               if(new_sl > open_price && new_sl > current_sl)
                 {
                  trade.PositionModify(posInfo.Ticket(), new_sl, posInfo.TakeProfit());
                 }
              }
            else // POSITION_TYPE_SELL
              {
               new_sl = current_price + atr * Trail_ATR_Multiplier;
               if(new_sl < open_price && (current_sl == 0 || new_sl < current_sl))
                 {
                  trade.PositionModify(posInfo.Ticket(), new_sl, posInfo.TakeProfit());
                 }
              }
           }
        }
     }
  }
//+------------------------------------------------------------------+
//| Check for a new bar                                              |
//+------------------------------------------------------------------+
bool IsNewBar()
  {
   static datetime last_bar_time=0;
   datetime current_bar_time=iTime(_Symbol,_Period,0);
   if(last_bar_time!=current_bar_time)
     {
      last_bar_time=current_bar_time;
      return(true);
     }
   return(false);
  }
//+------------------------------------------------------------------+
