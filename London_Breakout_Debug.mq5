//+------------------------------------------------------------------+
//|                          London_Breakout_Debug.mq5             |
//|                     Debug Version - Will Generate Trades       |
//+------------------------------------------------------------------+
#property copyright "Debug Version"
#property version   "1.00"

//--- Input Parameters
input group "=== London Breakout Settings ===="
input int    InpLondonStartHour = 6;     // London session start (GMT)
input int    InpLondonEndHour = 9;       // London session end (GMT)
input double InpBreakoutBuffer = 3.0;    // Breakout buffer (pips)
input double InpMinRiskReward = 1.8;     // Minimum R:R ratio
input int    InpMinRangePips = 3;        // Minimum range size (pips)
input int    InpMaxRangePips = 120;      // Maximum range size (pips)

input group "=== Debug Settings ===="
input bool   InpSkipRangeValidation = true;  // Skip Asian range requirement
input double InpFixedSLPips = 20.0;          // Fixed SL in pips (if range invalid)

input group "=== Risk Settings ===="
input double InpRiskPercentage = 1.0;    // Risk per trade (%)
input ulong  InpMagicNumber = 888999;    // Magic number

//--- Global Variables
struct DebugRange
{
    double high;
    double low;
    double rangePips;
    bool   isValid;
    bool   breakoutOccurred;
    datetime lastUpdate;
};
DebugRange asianRange;

bool tradingAllowed = true;
datetime lastTradeDay = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== London Breakout DEBUG Started ===");
    Print("Skip Range Validation: ", InpSkipRangeValidation);
    ResetRange();
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    static datetime lastBarTime = 0;
    datetime newBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(newBarTime > lastBarTime)
    {
        lastBarTime = newBarTime;
        
        MqlDateTime dt;
        TimeToStruct(TimeCurrent(), dt);
        int currentHour = dt.hour;
        
        // Reset trading permission daily
        if(lastTradeDay != iTime(_Symbol, PERIOD_D1, 0))
        {
            lastTradeDay = iTime(_Symbol, PERIOD_D1, 0);
            tradingAllowed = true;
            ResetRange();
            Print("NEW DAY - Trading reset, Hour: ", currentHour);
        }
        
        // Always update range during Asian session (23:00-6:00 GMT)
        if(currentHour >= 23 || currentHour < 6)
        {
            UpdateAsianRange();
        }
        
        // Validate range at 6 GMT
        if(currentHour == 6 && asianRange.lastUpdate != newBarTime)
        {
            ValidateRange();
            asianRange.lastUpdate = newBarTime;
        }
        
        // London breakout window
        if(currentHour >= InpLondonStartHour && currentHour < InpLondonEndHour && tradingAllowed)
        {
            CheckBreakout();
        }
        
        // Debug info every hour
        if(dt.min == 0)
        {
            Print("DEBUG Hour ", currentHour, ": Range Valid=", asianRange.isValid, 
                  " RangePips=", asianRange.rangePips, " TradingAllowed=", tradingAllowed,
                  " BreakoutOccurred=", asianRange.breakoutOccurred);
        }
    }
}

//+------------------------------------------------------------------+
//| Reset range                                                      |
//+------------------------------------------------------------------+
void ResetRange()
{
    asianRange.high = 0;
    asianRange.low = 999999;
    asianRange.rangePips = 0;
    asianRange.isValid = false;
    asianRange.breakoutOccurred = false;
    asianRange.lastUpdate = 0;
    Print("Range RESET");
}

//+------------------------------------------------------------------+
//| Update Asian range                                               |
//+------------------------------------------------------------------+
void UpdateAsianRange()
{
    double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 1); // Previous bar
    double currentLow = iLow(_Symbol, PERIOD_CURRENT, 1);
    
    if(asianRange.high == 0) asianRange.high = currentHigh;
    if(asianRange.low == 999999) asianRange.low = currentLow;
    
    if(currentHigh > asianRange.high) asianRange.high = currentHigh;
    if(currentLow < asianRange.low) asianRange.low = currentLow;
}

//+------------------------------------------------------------------+
//| Validate range                                                   |
//+------------------------------------------------------------------+
void ValidateRange()
{
    if(asianRange.high <= 0 || asianRange.low >= 999999)
    {
        Print("WARNING: Invalid range values - High:", asianRange.high, " Low:", asianRange.low);
        return;
    }
    
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    asianRange.rangePips = (asianRange.high - asianRange.low) / (point * 10);
    
    Print("Range calculated: ", asianRange.rangePips, " pips (High:", asianRange.high, " Low:", asianRange.low, ")");
    
    if(InpSkipRangeValidation)
    {
        asianRange.isValid = true;
        Print("Range validation SKIPPED - Trading enabled");
    }
    else if(asianRange.rangePips >= InpMinRangePips && asianRange.rangePips <= InpMaxRangePips)
    {
        asianRange.isValid = true;
        Print("Range VALID: ", asianRange.rangePips, " pips");
    }
    else
    {
        Print("Range INVALID: ", asianRange.rangePips, " pips (Required: ", InpMinRangePips, "-", InpMaxRangePips, ")");
        asianRange.isValid = false;
    }
}

//+------------------------------------------------------------------+
//| Check breakout                                                   |
//+------------------------------------------------------------------+
void CheckBreakout()
{
    if(!tradingAllowed || asianRange.breakoutOccurred)
    {
        return;
    }
    
    if(!InpSkipRangeValidation && !asianRange.isValid)
    {
        return;
    }
    
    double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 0);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double buffer = InpBreakoutBuffer * point * 10;
    
    bool bullishBreakout = false;
    bool bearishBreakout = false;
    
    if(InpSkipRangeValidation)
    {
        // Use previous 4-hour high/low as range
        double recentHigh = iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 16, 1));
        double recentLow = iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 16, 1));
        
        bullishBreakout = currentPrice > (recentHigh + buffer);
        bearishBreakout = currentPrice < (recentLow - buffer);
        
        Print("DEBUG: CurrentPrice=", currentPrice, " RecentHigh=", recentHigh, " RecentLow=", recentLow);
    }
    else
    {
        bullishBreakout = currentPrice > (asianRange.high + buffer);
        bearishBreakout = currentPrice < (asianRange.low - buffer);
    }
    
    if(bullishBreakout)
    {
        Print("BULLISH BREAKOUT DETECTED at ", currentPrice);
        ExecuteTrade(ORDER_TYPE_BUY, currentPrice);
        asianRange.breakoutOccurred = true;
        tradingAllowed = false; // One trade per day
    }
    else if(bearishBreakout)
    {
        Print("BEARISH BREAKOUT DETECTED at ", currentPrice);
        ExecuteTrade(ORDER_TYPE_SELL, currentPrice);
        asianRange.breakoutOccurred = true;
        tradingAllowed = false; // One trade per day
    }
}

//+------------------------------------------------------------------+
//| Execute trade                                                    |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE orderType, double price)
{
    double sl = 0;
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    
    if(InpSkipRangeValidation || !asianRange.isValid)
    {
        // Use fixed SL
        double slDistance = InpFixedSLPips * point * 10;
        sl = (orderType == ORDER_TYPE_BUY) ? price - slDistance : price + slDistance;
    }
    else
    {
        // Use range-based SL
        double buffer = 5 * point * 10; // 5 pip buffer
        sl = (orderType == ORDER_TYPE_BUY) ? asianRange.low - buffer : asianRange.high + buffer;
    }
    
    // Calculate TP
    double risk = MathAbs(price - sl);
    double reward = risk * InpMinRiskReward;
    double tp = (orderType == ORDER_TYPE_BUY) ? price + reward : price - reward;
    
    // Simple position size
    double lots = 0.01;
    
    Print("ATTEMPTING TRADE: ", EnumToString(orderType), " Price:", price, " SL:", sl, " TP:", tp, " Risk:", risk);
    
    // Execute
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lots;
    request.type = orderType;
    request.price = price;
    request.sl = NormalizeDouble(sl, _Digits);
    request.tp = NormalizeDouble(tp, _Digits);
    request.magic = InpMagicNumber;
    request.comment = "LB_Debug";
    
    if(OrderSend(request, result))
    {
        Print("TRADE SUCCESS: Ticket=", result.order, " at ", price);
    }
    else
    {
        Print("TRADE FAILED: Error=", result.retcode, " Comment=", result.comment);
    }
}