//+------------------------------------------------------------------+
//|                                              StochasticTrader.mq5 |
//|                        Copyright 2025, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters
input int    StochK_Period = 14;       // %K period
input int    StochD_Period = 3;        // %D period
input int    StochSlowing = 3;         // Slowing
input ENUM_MA_METHOD StochMethod = MODE_SMA; // Method
input ENUM_STO_PRICE StochPrice = STO_LOWHIGH; // Price field
input double StochOverbought = 80.0;   // Overbought level
input double StochOversold = 20.0;     // Oversold level
input double LotSize = 0.1;            // Lot size
input int    MaxTrades = 10;           // Maximum trades to open
input int    MagicNumber = 12345;      // Magic number

//--- Global variables
int stochastic_handle;
double stoch_main[];
double stoch_signal[];
datetime last_bar_time = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Initialize Stochastic indicator
   stochastic_handle = iStochastic(_Symbol, _Period, StochK_Period, StochD_Period, 
                                   StochSlowing, StochMethod, StochPrice);
   
   if(stochastic_handle == INVALID_HANDLE)
   {
      Print("Error creating Stochastic indicator: ", GetLastError());
      return(INIT_FAILED);
   }
   
   //--- Set array as series
   ArraySetAsSeries(stoch_main, true);
   ArraySetAsSeries(stoch_signal, true);
   
   Print("StochasticTrader EA initialized successfully");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Clean up
   if(stochastic_handle != INVALID_HANDLE)
      IndicatorRelease(stochastic_handle);
   
   Print("StochasticTrader EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check for new bar
   datetime current_bar_time = iTime(_Symbol, _Period, 0);
   if(current_bar_time == last_bar_time)
      return;
   
   last_bar_time = current_bar_time;
   
   //--- Check if we have reached max trades
   if(CountMyTrades() >= MaxTrades)
      return;
   
   //--- Get Stochastic values
   if(CopyBuffer(stochastic_handle, 0, 0, 3, stoch_main) < 3 ||
      CopyBuffer(stochastic_handle, 1, 0, 3, stoch_signal) < 3)
   {
      Print("Error copying Stochastic data: ", GetLastError());
      return;
   }
   
   //--- Check for buy signal (oversold crossover)
   if(stoch_main[2] < StochOversold && stoch_signal[2] < StochOversold &&
      stoch_main[1] > stoch_signal[1] && stoch_main[2] <= stoch_signal[2])
   {
      if(OpenTrade(ORDER_TYPE_BUY))
      {
         Print("BUY trade opened - Stoch Main: ", stoch_main[1], " Signal: ", stoch_signal[1]);
      }
   }
   
   //--- Check for sell signal (overbought crossover)
   if(stoch_main[2] > StochOverbought && stoch_signal[2] > StochOverbought &&
      stoch_main[1] < stoch_signal[1] && stoch_main[2] >= stoch_signal[2])
   {
      if(OpenTrade(ORDER_TYPE_SELL))
      {
         Print("SELL trade opened - Stoch Main: ", stoch_main[1], " Signal: ", stoch_signal[1]);
      }
   }
}

//+------------------------------------------------------------------+
//| Open a trade                                                     |
//+------------------------------------------------------------------+
bool OpenTrade(ENUM_ORDER_TYPE order_type)
{
   MqlTradeRequest request = {};
   MqlTradeResult result = {};
   
   //--- Get current prices
   MqlTick tick;
   if(!SymbolInfoTick(_Symbol, tick))
   {
      Print("Error getting tick data: ", GetLastError());
      return false;
   }
   
   //--- Fill the request structure
   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = LotSize;
   request.type = order_type;
   request.price = (order_type == ORDER_TYPE_BUY) ? tick.ask : tick.bid;
   request.deviation = 10;
   request.magic = MagicNumber;
   request.comment = "StochTrader_" + EnumToString(order_type);
   
   //--- Send the request
   if(!OrderSend(request, result))
   {
      Print("OrderSend failed: ", GetLastError(), " - ", result.comment);
      return false;
   }
   
   if(result.retcode == TRADE_RETCODE_DONE)
   {
      return true;
   }
   else
   {
      Print("Trade failed with retcode: ", result.retcode, " - ", result.comment);
      return false;
   }
}

//+------------------------------------------------------------------+
//| Count trades opened by this EA                                   |
//+------------------------------------------------------------------+
int CountMyTrades()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
            PositionGetInteger(POSITION_MAGIC) == MagicNumber)
         {
            count++;
         }
      }
   }
   return count;
}