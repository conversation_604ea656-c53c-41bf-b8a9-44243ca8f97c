//+------------------------------------------------------------------+
//|                                     ICT_SilverBullet_Corrected.mq5 |
//|                        Copyright 2025, Gemini Trading Systems      |
//|                       (Full Suite - Compilation Fixed v4.10)       |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Gemini Trading Systems (Full Suite)"
#property link      "https://www.google.com"
#property version   "4.10" // Compilation Fixes

#include <Trade/Trade.mqh>

//--- Input parameters
input group "=== ICT Silver Bullet Settings ==="
input bool   InpEnableLondonSession = true;        // Enable London Session (3-4 AM EST)
input bool   InpEnableNYAMSession = true;          // Enable NY AM Session (10-11 AM EST)
input bool   InpEnableNYPMSession = true;          // Enable NY PM Session (2-3 PM EST)
input int    InpLookbackPeriod = 100;              // Lookback period for liquidity levels
input bool   InpWaitForFVGRetracement = true;      // Wait for price to retrace into FVG for entry
input double InpFVGEntryPercent = 50.0;            // FVG entry level (0-100%), 50% is equilibrium
input bool   InpRequireMSS = true;                 // Require Market Structure Shift for entry
input int    InpMSSLookback = 15;                  // Lookback bars for MSS swing point
input double InpMinFVGSize = 1.0;                  // Minimum FVG size in pips

input group "=== Risk Management ==="
input double InpRiskPercentage = 1.0;              // Risk per trade (%)
input int    InpMaxConcurrentTrades = 1;           // Max concurrent trades
input double InpMaxDailyLossPercent = 5.0;         // Max daily loss limit (%)
input int    InpMaxConsecutiveLosses = 5;          // Max consecutive losses allowed
input double InpMaxSpreadPips = 2.0;               // Max allowed spread in pips
input uint   InpMaxSlippagePoints = 10;            // Max allowed slippage in points
input bool   InpUseVolatilityFilter = true;        // Enable Volatility Filter
input int    InpATR_SMA_Period = 50;               // Period for SMA of ATR
input double InpMinATR_vs_SMA_Ratio = 0.8;         // Min ATR vs SMA(ATR) to trade
input double InpMaxATR_vs_SMA_Ratio = 3.0;         // Max ATR vs SMA(ATR) to trade

input group "=== Exit Settings ==="
enum ENUM_EXIT_STRATEGY {
    FIXED_RR,           // Fixed Risk:Reward
    OPPOSING_FVG,       // Exit on opposing FVG
    LIQUIDITY_TARGET    // Target nearest liquidity level
};
input ENUM_EXIT_STRATEGY InpExitStrategy = LIQUIDITY_TARGET; // Exit Strategy
input double InpFixedRiskReward = 2.0;             // R:R for fixed TP (used as fallback)
input double InpMinRiskReward = 1.5;               // Minimum R:R required for any trade
input double InpDynamicTPBufferPips = 2.0;         // Buffer in pips for dynamic TP

input bool   InpUseBreakeven = true;               // Enable Breakeven
input double InpBreakevenPips = 10.0;              // Pips in profit to move to breakeven
input double InpBreakevenBufferPips = 2.0;         // Buffer in pips for breakeven SL
input bool   InpUseTrailingStop = true;            // Enable Trailing Stop
input double InpTrailStartPips = 20.0;             // Pips in profit to activate trailing SL
input int    InpTrailATRPeriod = 14;               // ATR Period for Trailing Stop
input double InpTrailDistanceATR = 2.0;            // Trailing distance in ATR multiples

input group "=== Timeframe Settings ==="
input ENUM_TIMEFRAMES InpStructureTimeframe = PERIOD_H1; // Timeframe for liquidity highs/lows
input ENUM_TIMEFRAMES InpExecutionTimeframe = PERIOD_M5; // Execution timeframe for FVG/Entry

//--- Notification Settings
input group "=== Notification Settings ==="
input bool   InpEnableAlerts = true;            // Enable On-Screen Alerts
input bool   InpEnablePushNotifications = false; // Enable Push Notifications (to mobile)
input bool   InpEnableEmailNotifications = false; // Enable Email Notifications
input bool   InpNotifyOnOpen = true;            // Notify on New Trade Open
input bool   InpNotifyOnClose = true;           // Notify on Trade Close
input bool   InpNotifyOnError = true;           // Notify on Trading Error
input bool   InpNotifyOnRisk = true;            // Notify when a risk limit is hit

input group "=== General Settings ==="
input ulong  InpMagicNumber = 777888;
input string InpTradeComment = "ICT_SB_Corrected";

//--- Global variables
CTrade trade;
int atr_handle;
int atr_sma_handle;

//--- Time management (GMT time - user must ensure these match broker's GMT time)
int londonStartHour = 7; int londonEndHour = 8;
int nyamStartHour = 14; int nyamEndHour = 15;
int nypmStartHour = 18; int nypmEndHour = 19;

//--- Risk Management Globals
double   dailyStartBalance;
bool     dailyLossLimitHit = false;
int      consecutiveLosses = 0;
datetime lastTradeDay = 0;

//--- Strategy structures
struct FairValueGap { datetime time; double upperLevel; double lowerLevel; bool isBullish; };
struct LiquidityLevel { datetime time; double level; bool isBuySide; };

//--- Position State Tracking for Breakeven
struct PositionState
{
   ulong   ticket;
   bool    breakeven_applied;
};
PositionState managedPositions[];

//--- Dynamic arrays for storing levels
LiquidityLevel liquidityLevels[];
FairValueGap fvgArray[];

//+------------------------------------------------------------------+
//| Send Notifications                                               |
//+------------------------------------------------------------------+
void SendNotification(string subject, string message)
{
    if(IsStopped()) return;
    if(InpEnableAlerts) Alert(subject, "\n", message);
    if(InpEnablePushNotifications)
    {
        string combined_message = subject + ": " + message;
        if(StringLen(combined_message) > 255) combined_message = StringSubstr(combined_message, 0, 255);
        SendNotification(combined_message);
    }
    if(InpEnableEmailNotifications) SendMail(subject, message);
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetTypeFillingBySymbol(_Symbol);
    trade.SetDeviationInPoints(InpMaxSlippagePoints);
    
    atr_handle = iATR(_Symbol, InpExecutionTimeframe, InpTrailATRPeriod);
    if(atr_handle == INVALID_HANDLE)
    {
        Print("Error creating ATR indicator for trailing stop.");
        return INIT_FAILED;
    }

    atr_sma_handle = iMA(_Symbol, InpExecutionTimeframe, InpATR_SMA_Period, 0, MODE_SMA, atr_handle);
    if(atr_sma_handle == INVALID_HANDLE)
    {
        Print("Error creating SMA of ATR indicator for volatility filter.");
        return INIT_FAILED;
    }
    
    ArrayResize(liquidityLevels, 0);
    ArrayResize(fvgArray, 0);
    
    dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    lastTradeDay = TimeCurrent();
    
    Print("=== ICT Silver Bullet Corrected EA Initialized (v4.10) ===");
    Print("Exit Strategy: ", EnumToString(InpExitStrategy));
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) 
{ 
    IndicatorRelease(atr_handle); 
    IndicatorRelease(atr_sma_handle);
    Print("EA Deinitialized."); 
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    static datetime lastBarTime = 0;
    
    ManageTradeExits(); // Manage BE and Trailing Stops on every tick for precision

    datetime newBarTime = (datetime)SeriesInfoInteger(_Symbol, InpExecutionTimeframe, SERIES_LASTBAR_DATE);

    if(newBarTime > lastBarTime)
    {
        lastBarTime = newBarTime;
        
        CheckNewDay();
        
        if(InpExitStrategy == OPPOSING_FVG && PositionsTotal() > 0)
        {
            ManageDynamicExits();
        }

        if(!IsRiskManaged()) return;
        if(PositionsTotal() >= InpMaxConcurrentTrades) return;

        LookForSilverBulletSetup();
    }
}

//+------------------------------------------------------------------+
//| Main logic sequence for finding new trades                       |
//+------------------------------------------------------------------+
void LookForSilverBulletSetup()
{
    if(!IsInSilverBulletSession()) return;

    FindLiquidityLevels();

    bool isBuySideSweep = false;
    bool isSellSideSweep = false;

    MqlRates execRates[];
    if(CopyRates(_Symbol, InpExecutionTimeframe, 0, 2, execRates) < 2) return;
    double currentHigh = execRates[0].high;
    double currentLow = execRates[0].low;

    for(int i = 0; i < ArraySize(liquidityLevels); i++)
    {
        if(liquidityLevels[i].isBuySide && currentHigh > liquidityLevels[i].level)
        { isBuySideSweep = true; ArrayRemove(liquidityLevels, i, 1); break; }
        
        if(!liquidityLevels[i].isBuySide && currentLow < liquidityLevels[i].level)
        { isSellSideSweep = true; ArrayRemove(liquidityLevels, i, 1); break; }
    }

    if(isBuySideSweep || isSellSideSweep)
    {
        // --- CONFIRMATION STEP: Market Structure Shift (MSS) ---
        if(InpRequireMSS)
        {
            bool mss_confirmed = CheckForMarketStructureShift(isBuySideSweep);
            if(!mss_confirmed)
            {
                // Optional: Add a notification for failed MSS
                return; // MSS not confirmed, wait for next setup
            }
        }

        // --- FINAL STEP: Find FVG and Execute ---
        FindFairValueGaps();
        
        if(isBuySideSweep)
        { for(int i = ArraySize(fvgArray) - 1; i >= 0; i--) { if(!fvgArray[i].isBullish) { ExecuteTrade(ORDER_TYPE_SELL, fvgArray[i]); return; } } }
        else if(isSellSideSweep)
        { for(int i = ArraySize(fvgArray) - 1; i >= 0; i--) { if(fvgArray[i].isBullish) { ExecuteTrade(ORDER_TYPE_BUY, fvgArray[i]); return; } } }

    }
}

//+------------------------------------------------------------------+
//| Manage Dynamic Exits for Open Positions                          |
//+------------------------------------------------------------------+
void ManageDynamicExits()
{
    FindFairValueGaps();
    if(ArraySize(fvgArray) == 0) return;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetInteger(POSITION_MAGIC) == InpMagicNumber && PositionGetString(POSITION_SYMBOL) == _Symbol)
        {
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            FairValueGap latestFVG = fvgArray[ArraySize(fvgArray) - 1];

            if(posType == POSITION_TYPE_BUY && !latestFVG.isBullish)
            {
                if(InpNotifyOnClose) SendNotification("Dynamic Exit", StringFormat("New Bearish FVG formed. Closing BUY position #%d", ticket));
                trade.PositionClose(ticket);
            }
            else if(posType == POSITION_TYPE_SELL && latestFVG.isBullish)
            {
                if(InpNotifyOnClose) SendNotification("Dynamic Exit", StringFormat("New Bullish FVG formed. Closing SELL position #%d", ticket));
                trade.PositionClose(ticket);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Find the nearest liquidity level to target for Take Profit       |
//+------------------------------------------------------------------+
double FindNearestLiquidityTarget(ENUM_ORDER_TYPE orderType, double entryPrice)
{
    double targetLevel = 0;
    double closestDistance = 999999;

    if(orderType == ORDER_TYPE_BUY)
    {
        // Find the closest buy-side liquidity ABOVE entry
        for(int i = 0; i < ArraySize(liquidityLevels); i++)
        {
            if(liquidityLevels[i].isBuySide && liquidityLevels[i].level > entryPrice)
            {
                double distance = liquidityLevels[i].level - entryPrice;
                if(distance < closestDistance)
                {
                    closestDistance = distance;
                    targetLevel = liquidityLevels[i].level;
                }
            }
        }
    }
    else // ORDER_TYPE_SELL
    {
        // Find the closest sell-side liquidity BELOW entry
        for(int i = 0; i < ArraySize(liquidityLevels); i++)
        {
            if(!liquidityLevels[i].isBuySide && liquidityLevels[i].level < entryPrice)
            {
                double distance = entryPrice - liquidityLevels[i].level;
                if(distance < closestDistance)
                {
                    closestDistance = distance;
                    targetLevel = liquidityLevels[i].level;
                }
            }
        }
    }
    return targetLevel;
}

//+------------------------------------------------------------------+
//| Execute a trade based on the FVG                                 |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE orderType, FairValueGap &fvg)
{
    double entryPrice, sl, tp = 0;
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double tp_buffer = InpDynamicTPBufferPips * point * 10;

    if(orderType == ORDER_TYPE_BUY)
    {
        sl = fvg.lowerLevel - (InpBreakevenBufferPips * point * 10); // Use buffer for SL placement
        if(InpWaitForFVGRetracement)
        {
            entryPrice = fvg.lowerLevel + (fvg.upperLevel - fvg.lowerLevel) * (InpFVGEntryPercent / 100.0);
        }
        else
        {
            entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        }
    }
    else // ORDER_TYPE_SELL
    {
        sl = fvg.upperLevel + (InpBreakevenBufferPips * point * 10); // Use buffer for SL placement
        if(InpWaitForFVGRetracement)
        {
            entryPrice = fvg.upperLevel - (fvg.upperLevel - fvg.lowerLevel) * (InpFVGEntryPercent / 100.0);
        }
        else
        {
            entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        }
    }
    
    // --- Take Profit Logic ---
    switch(InpExitStrategy)
    {
        case LIQUIDITY_TARGET:
        {
            double target_level = FindNearestLiquidityTarget(orderType, entryPrice);
            if(target_level > 0)
            {
                tp = target_level + (orderType == ORDER_TYPE_BUY ? -tp_buffer : tp_buffer);
            }
            // Fallback to fixed R:R if no target is found
            else
            {
                tp = (orderType == ORDER_TYPE_BUY) ? entryPrice + MathAbs(entryPrice - sl) * InpFixedRiskReward : entryPrice - MathAbs(entryPrice - sl) * InpFixedRiskReward;
            }
            break;
        }
            
        case FIXED_RR:
        {
            tp = (orderType == ORDER_TYPE_BUY) ? entryPrice + MathAbs(entryPrice - sl) * InpFixedRiskReward : entryPrice - MathAbs(entryPrice - sl) * InpFixedRiskReward;
            break;
        }
            
        case OPPOSING_FVG:
        {
            tp = 0; // TP is managed by ManageDynamicExits()
            break;
        }
    }

    // --- Minimum R:R Check ---
    double risk_dist = MathAbs(entryPrice - sl);
    double reward_dist = MathAbs(tp - entryPrice);
    if (tp > 0 && risk_dist > 0 && (reward_dist / risk_dist) < InpMinRiskReward)
    {
        if(InpNotifyOnError) SendNotification("Trade Skipped", StringFormat("Dynamic TP did not meet min R:R of 1:%.1f.", InpMinRiskReward));
        return; // Skip trade
    }

    double lots = CalculatePositionSize(sl);
    if(lots <= 0) 
    {
        if(InpNotifyOnError) SendNotification("Trade Error", "Invalid lot size calculated.");
        return;
    }

    bool result = false;
    if(InpWaitForFVGRetracement)
    {
        if(orderType == ORDER_TYPE_BUY) result = trade.BuyLimit(lots, entryPrice, _Symbol, sl, tp, 0, 0, InpTradeComment);
        else result = trade.SellLimit(lots, entryPrice, _Symbol, sl, tp, 0, 0, InpTradeComment);
    }
    else // Market Order
    {
        if(orderType == ORDER_TYPE_BUY)
        {
            result = trade.Buy(lots, _Symbol, 0, sl, tp, InpTradeComment);
        }
        else
        {
            result = trade.Sell(lots, _Symbol, 0, sl, tp, InpTradeComment);
        }
    }

    if(result)
    {
        if(InpNotifyOnOpen) SendNotification("New Trade", StringFormat("%s @ %.5f, SL: %.5f", EnumToString(orderType), entryPrice, sl));
    }
    else
    {
        if(InpNotifyOnError) SendNotification("Execution Error", StringFormat("Failed. Error %d: %s", trade.ResultRetcode(), trade.ResultComment()));
    }
}

//+------------------------------------------------------------------+
//| Manage Breakeven and Trailing Stops for Open Positions           |
//+------------------------------------------------------------------+
void ManageTradeExits()
{
    if(!InpUseBreakeven && !InpUseTrailingStop) return;

    double atr_buffer[];
    if(InpUseTrailingStop)
    {
      ArraySetAsSeries(atr_buffer, true);
      if(CopyBuffer(atr_handle, 0, 0, 1, atr_buffer) < 1) return;
    }
    double current_atr = InpUseTrailingStop ? atr_buffer[0] : 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        // Find the corresponding managed state for this position
        int pos_idx = -1;
        ulong ticket = PositionGetTicket(i);
        if(PositionGetInteger(POSITION_MAGIC) != InpMagicNumber || PositionGetString(POSITION_SYMBOL) != _Symbol) continue;

        for(int j = 0; j < ArraySize(managedPositions); j++)
        {
            if(managedPositions[j].ticket == ticket)
            {
                pos_idx = j;
                break;
            }
        }

        if(pos_idx != -1)
        {
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_sl = PositionGetDouble(POSITION_SL);
            ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
            double current_price = SymbolInfoDouble(_Symbol, (type == POSITION_TYPE_BUY) ? SYMBOL_BID : SYMBOL_ASK);
            double profit_pips = (type == POSITION_TYPE_BUY) ? (current_price - open_price) / point / 10 : (open_price - current_price) / point / 10;

            // --- Breakeven Logic ---
            if (InpUseBreakeven && !managedPositions[pos_idx].breakeven_applied && profit_pips >= InpBreakevenPips)
            {
                double breakeven_sl = open_price + ((type == POSITION_TYPE_BUY ? 1 : -1) * InpBreakevenBufferPips * point * 10);
                bool should_modify = (type == POSITION_TYPE_BUY && breakeven_sl > current_sl) || (type == POSITION_TYPE_SELL && breakeven_sl < current_sl);

                if (should_modify)
                {
                    if(trade.PositionModify(ticket, breakeven_sl, PositionGetDouble(POSITION_TP)))
                    {
                        managedPositions[pos_idx].breakeven_applied = true;
                        if(InpNotifyOnClose) SendNotification("Breakeven", StringFormat("Position #%d moved to breakeven.", ticket));
                    }
                }
            }

            // --- Trailing Stop Logic ---
            if (InpUseTrailingStop && profit_pips > InpTrailStartPips)
            {
                double new_sl = current_price + ((type == POSITION_TYPE_BUY ? -1 : 1) * current_atr * InpTrailDistanceATR);
                bool should_modify = (type == POSITION_TYPE_BUY && new_sl > current_sl) || (type == POSITION_TYPE_SELL && new_sl < current_sl);

                if(should_modify)
                {
                    if(trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP)))
                    {
                        // Optional: Add a notification for trailing stop updates if desired
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Risk Management Checks                                           |
//+------------------------------------------------------------------+
bool IsRiskManaged()
{
    if(InpUseVolatilityFilter && !IsVolatilityFavorable())
    {
        // Optional: Add notification for unfavorable volatility
        return false;
    }

    if(dailyLossLimitHit)
    {
        static datetime last_alert_time = 0;
        if(TimeCurrent() - last_alert_time > 3600)
        {
            if(InpNotifyOnRisk) SendNotification("Risk Alert", "Daily loss limit hit. Trading disabled.");
            last_alert_time = TimeCurrent();
        }
        return false;
    }

    if(consecutiveLosses >= InpMaxConsecutiveLosses)
    {
        if(InpNotifyOnRisk) SendNotification("Risk Alert", StringFormat("Max consecutive losses (%d) reached. Trading paused.", InpMaxConsecutiveLosses));
        return false;
    }

    double spread_pips = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / (SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 10);
    if(spread_pips > InpMaxSpreadPips)
    {
        if(InpNotifyOnRisk) SendNotification("Risk Alert", StringFormat("Spread (%.1f) is too high. Trading paused.", spread_pips));
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check if volatility is within acceptable range                   |
//+------------------------------------------------------------------+
bool IsVolatilityFavorable()
{
    double atr_buffer[1], sma_atr_buffer[1];

    if(CopyBuffer(atr_handle, 0, 0, 1, atr_buffer) < 1) return false;
    if(CopyBuffer(atr_sma_handle, 0, 0, 1, sma_atr_buffer) < 1) return false;

    double current_atr = atr_buffer[0];
    double sma_atr = sma_atr_buffer[0];

    if(sma_atr <= 0) return false; // Avoid division by zero

    double atr_ratio = current_atr / sma_atr;

    if(atr_ratio < InpMinATR_vs_SMA_Ratio) return false; // Volatility too low
    if(atr_ratio > InpMaxATR_vs_SMA_Ratio) return false; // Volatility too high

    return true;
}

//+------------------------------------------------------------------+
//| Check for a Market Structure Shift after a liquidity sweep       |
//+------------------------------------------------------------------+
bool CheckForMarketStructureShift(bool isBearishShift)
{
    MqlRates rates[];
    if(CopyRates(_Symbol, InpExecutionTimeframe, 0, InpMSSLookback, rates) < InpMSSLookback) return false;

    if(isBearishShift) // After buy-side sweep, look for break of swing LOW
    {
        double recent_swing_low = 999999;
        // Find the lowest low in the lookback period (excluding the current bar)
        for(int i = 1; i < InpMSSLookback; i++) { if(rates[i].low < recent_swing_low) recent_swing_low = rates[i].low; }
        
        // Check if the current bar has broken below this swing low
        if(rates[0].low < recent_swing_low) return true;
    }
    else // After sell-side sweep, look for break of swing HIGH
    {
        double recent_swing_high = 0;
        // Find the highest high in the lookback period (excluding the current bar)
        for(int i = 1; i < InpMSSLookback; i++) { if(rates[i].high > recent_swing_high) recent_swing_high = rates[i].high; }
        
        // Check if the current bar has broken above this swing high
        if(rates[0].high > recent_swing_high) return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| Check if currently in Silver Bullet session                      |
//+------------------------------------------------------------------+
bool IsInSilverBulletSession()
{
    MqlDateTime dt; TimeToStruct(TimeCurrent(), dt);
    int h = dt.hour;
    if((InpEnableLondonSession && h >= londonStartHour && h < londonEndHour) ||
       (InpEnableNYAMSession && h >= nyamStartHour && h < nyamEndHour) ||
       (InpEnableNYPMSession && h >= nypmStartHour && h < nypmEndHour)) return true;
    return false;
}

//+------------------------------------------------------------------+
//| Find swing highs and lows on the structure timeframe             |
//+------------------------------------------------------------------+
void FindLiquidityLevels()
{
    ArrayResize(liquidityLevels, 0);
    MqlRates rates[];
    if(CopyRates(_Symbol, InpStructureTimeframe, 0, InpLookbackPeriod, rates) < InpLookbackPeriod) return;

    for(int i = 2; i < InpLookbackPeriod - 2; i++)
    {
        if(rates[i].high > rates[i-1].high && rates[i].high > rates[i-2].high && rates[i].high > rates[i+1].high && rates[i].high > rates[i+2].high)
        {
            LiquidityLevel l;
            l.time = rates[i].time;
            l.level = rates[i].high;
            l.isBuySide = true;
            int size = ArraySize(liquidityLevels); ArrayResize(liquidityLevels, size + 1); liquidityLevels[size] = l;
        }

        if(rates[i].low < rates[i-1].low && rates[i].low < rates[i-2].low && rates[i].low < rates[i+1].low && rates[i].low < rates[i+2].low)
        {
            LiquidityLevel l;
            l.time = rates[i].time;
            l.level = rates[i].low;
            l.isBuySide = false;
            int size = ArraySize(liquidityLevels); ArrayResize(liquidityLevels, size + 1); liquidityLevels[size] = l;
        }
    }
}

//+------------------------------------------------------------------+
//| Find Fair Value Gaps on the execution timeframe                  |
//+------------------------------------------------------------------+
void FindFairValueGaps()
{
    ArrayResize(fvgArray, 0);
    MqlRates rates[];
    if(CopyRates(_Symbol, InpExecutionTimeframe, 0, 50, rates) < 50) return;

    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double minGap = InpMinFVGSize * point * 10;

    for(int i = 2; i < 50; i++)
    {
        if(rates[i-2].low > rates[i].high && (rates[i-2].low - rates[i].high) >= minGap)
        {
            FairValueGap fvg;
            fvg.time = rates[i-1].time;
            fvg.upperLevel = rates[i-2].low;
            fvg.lowerLevel = rates[i].high;
            fvg.isBullish = true;
            int size = ArraySize(fvgArray); ArrayResize(fvgArray, size + 1); fvgArray[size] = fvg;
        }

        if(rates[i-2].high < rates[i].low && (rates[i].low - rates[i-2].high) >= minGap)
        {
            FairValueGap fvg;
            fvg.time = rates[i-1].time;
            fvg.upperLevel = rates[i].low;
            fvg.lowerLevel = rates[i-2].high;
            fvg.isBullish = false;
            int size = ArraySize(fvgArray); ArrayResize(fvgArray, size + 1); fvgArray[size] = fvg;
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate Position Size based on risk percentage                 |
//+------------------------------------------------------------------+
double CalculatePositionSize(double stopLoss)
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = balance * (InpRiskPercentage / 100.0);
    double price = (stopLoss < SymbolInfoDouble(_Symbol, SYMBOL_BID)) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl_dist = MathAbs(price - stopLoss);
    if(sl_dist == 0) return 0;

    double lot_size = 0;
    double tick_val = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    if(tick_val > 0 && tick_size > 0) { double risk_per_lot = (sl_dist / tick_size) * tick_val; if(risk_per_lot > 0) lot_size = risk_amount / risk_per_lot; }

    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN), max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX), step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    lot_size = MathRound(lot_size / step) * step;
    if(lot_size < min_lot) lot_size = min_lot;
    if(lot_size > max_lot) lot_size = max_lot;

    return lot_size;
}

//+------------------------------------------------------------------+
//| Handle trade events to track P/L and risk                        |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans, const MqlTradeRequest& request, const MqlTradeResult& result)
{
    if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
    {
        if(!HistoryDealSelect(trans.deal)) return;
        if(HistoryDealGetInteger(trans.deal, DEAL_MAGIC) != InpMagicNumber) return;

        long deal_entry = HistoryDealGetInteger(trans.deal, DEAL_ENTRY);
        ulong position_id = HistoryDealGetInteger(trans.deal, DEAL_POSITION_ID);

        // A new position was opened
        if(deal_entry == DEAL_ENTRY_IN)
        {
            PositionState newPos;
            newPos.ticket = position_id;
            newPos.breakeven_applied = false;

            int size = ArraySize(managedPositions);
            ArrayResize(managedPositions, size + 1);
            managedPositions[size] = newPos;
        }
        // A position was fully closed
        else if(deal_entry == DEAL_ENTRY_OUT)
        {
            if(!PositionSelectByTicket(position_id)) // Check if it's fully closed
            {
                double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
                if(profit < 0) consecutiveLosses++; else consecutiveLosses = 0;

                if(InpNotifyOnClose) SendNotification("Trade Closed", StringFormat("Position #%d closed. Profit: %.2f", position_id, profit));

                if(!dailyLossLimitHit)
                {
                    double daily_loss_pct = (dailyStartBalance - AccountInfoDouble(ACCOUNT_BALANCE)) / dailyStartBalance * 100.0;
                    if(daily_loss_pct >= InpMaxDailyLossPercent)
                    {
                        dailyLossLimitHit = true;
                        if(InpNotifyOnRisk) SendNotification("RISK LIMIT HIT", StringFormat("Daily loss limit of %.2f%% reached.", InpMaxDailyLossPercent));
                    }
                }

                // Remove from managed array
                for(int i = ArraySize(managedPositions) - 1; i >= 0; i--)
                {
                    if(managedPositions[i].ticket == position_id) { ArrayRemove(managedPositions, i, 1); break; }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Reset daily counters at the start of a new day                   |
//+------------------------------------------------------------------+
void CheckNewDay()
{
    MqlDateTime dt; TimeToStruct(TimeCurrent(), dt);
    MqlDateTime last_dt; TimeToStruct(lastTradeDay, last_dt);

    if(dt.day_of_year != last_dt.day_of_year)
    {
        dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        dailyLossLimitHit = false;
        consecutiveLosses = 0;
        lastTradeDay = TimeCurrent();
        SendNotification("New Day", "Daily risk limits have been reset.");
    }
}