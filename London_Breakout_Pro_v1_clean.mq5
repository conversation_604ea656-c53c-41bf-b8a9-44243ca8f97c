//+------------------------------------------------------------------+
//|                        London_Breakout_Pro_v1.mq5              |
//|                     Copyright 2025, Professional Trading       |
//|              Comprehensive London Breakout Strategy             |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Professional Trading"
#property link      "https://www.protrading.com"  
#property version   "1.00"

// Using standard MQL5 trading functions instead of CTrade class

//--- Asian Range Detection
input group "=== Asian Range Settings ===="
input int             InpAsianStartHour = 23;              // Asian session start (GMT)
input int             InpAsianEndHour = 6;                 // Asian session end (GMT)
input int             InpMinRangePips = 5;                 // Minimum range size (pips)
input int             InpMaxRangePips = 150;               // Maximum range size (pips)
input double          InpRangeValidityPercent = 50.0;      // % of Asian session for valid range

//--- London Breakout Settings
input group "=== London Breakout Settings ===="
input int             InpLondonStartHour = 7;              // London session start (GMT)
input int             InpLondonEndHour = 10;               // London session end (GMT) 
input double          InpBreakoutBuffer = 2.0;             // Breakout buffer (pips)
input bool            InpRequireVolumeConfirmation = false; // Require volume spike
input double          InpVolumeMultiplier = 1.2;           // Volume vs average multiplier
input int             InpVolumeLookback = 20;              // Volume average lookback
input bool            InpAllowBothDirections = true;       // Allow long and short breakouts

//--- Advanced Risk Management
input group "=== Advanced Risk Management ===="
input double          InpRiskPercentage = 1.0;             // Risk per trade (%)
input double          InpMaxDailyLoss = 3.0;               // Max daily loss (%)
input double          InpMaxWeeklyLoss = 8.0;              // Max weekly loss (%)
input int             InpMaxConcurrentTrades = 2;          // Max concurrent trades
input double          InpMaxDrawdownPercent = 15.0;        // Max drawdown before pause
input int             InpMaxConsecutiveLosses = 4;         // Max consecutive losses
input double          InpCorrelationLimit = 0.7;           // Max correlation between pairs
input bool            InpUseAdaptiveRisk = true;           // Adaptive risk scaling
input double          InpMaxPositionSize = 8.0;            // Max position size (% of account)
input double          InpEquityProtection = 95.0;          // Stop trading below % of peak equity

//--- Dynamic Exit Strategy
input group "=== Dynamic Exit Strategy ===="
input double          InpMinRiskReward = 1.5;              // Minimum R:R ratio
input double          InpBaseRiskReward = 2.0;             // Base R:R target
input double          InpMaxRiskReward = 4.0;              // Maximum R:R target
input bool            InpUseDynamicTP = true;              // Use dynamic take profit
input bool            InpUseBreakeven = true;              // Enable breakeven
input double          InpBreakevenRR = 0.8;                // R:R to move to breakeven
input double          InpBreakevenBuffer = 0.2;            // Breakeven buffer R:R
input bool            InpUsePartialClose = true;           // Enable partial profit taking
input double          InpPartialClose1_RR = 1.0;           // First partial close R:R
input double          InpPartialClose1_Percent = 30.0;     // First partial close %
input double          InpPartialClose2_RR = 2.0;           // Second partial close R:R
input double          InpPartialClose2_Percent = 40.0;     // Second partial close %

//--- Smart Trailing Stop
input group "=== Smart Trailing Stop ===="
input bool            InpUseTrailingStop = true;           // Enable trailing stop
input double          InpTrailStartRR = 1.2;               // R:R to start trailing
input ENUM_TIMEFRAMES InpTrailTimeframe = PERIOD_M15;      // Timeframe for trailing
input int             InpTrailATRPeriod = 14;              // ATR period for trailing
input double          InpTrailATRMultiplier = 2.0;         // ATR multiplier for distance
input bool            InpUseSwingTrailing = true;          // Use swing high/low trailing
input int             InpSwingLookback = 10;               // Swing lookback period

//--- Time Management
input group "=== Time Management ===="
input bool            InpUseTimeFilters = true;            // Enable time filters
input bool            InpAvoidNews = true;                 // Avoid high impact news
input int             InpNewsBufferMinutes = 30;           // News avoidance buffer
input bool            InpCloseBeforeWeekend = true;        // Close positions before weekend
input int             InpFridayCloseHour = 20;             // Friday close hour (GMT)
input int             InpMaxTradeHours = 6;                // Max hours to keep trade open
input int             InpCooldownMinutes = 60;             // Cooldown between trades

//--- Market Condition Filters
input group "=== Market Condition Filters ===="
input bool            InpUseVolatilityFilter = false;      // Use volatility filter
input int             InpATRPeriod = 14;                   // ATR period for volatility
input double          InpMinVolatilityRatio = 0.5;         // Min ATR vs average ratio
input double          InpMaxVolatilityRatio = 5.0;         // Max ATR vs average ratio
input int             InpATRAveragePeriod = 50;             // ATR average period
input bool            InpUseSpreadFilter = false;          // Use spread filter
input double          InpMaxSpreadPips = 5.0;              // Max allowed spread
input bool            InpUseLiquidityFilter = false;       // Use liquidity filter
input double          InpMinLiquidityScore = 0.6;          // Min liquidity score (0-1)

//--- Multi-Pair Optimization
input group "=== Multi-Pair Settings ===="
input bool            InpOptimizeForEURUSD = true;         // Optimize for EURUSD
input bool            InpOptimizeForGBPUSD = true;         // Optimize for GBPUSD
input bool            InpOptimizeForUSDJPY = true;         // Optimize for USDJPY
input bool            InpOptimizeForAUDUSD = true;         // Optimize for AUDUSD
input double          InpPairSpecificMultiplier = 1.0;     // Pair-specific adjustment

//--- Notifications & Monitoring
input group "=== Notifications & Monitoring ===="
input bool            InpEnableAlerts = true;              // Enable alerts
input bool            InpEnablePushNotifications = false;  // Enable push notifications
input bool            InpEnableEmailNotifications = false; // Enable email notifications
input bool            InpNotifyOnSetup = true;             // Notify on range setup
input bool            InpNotifyOnBreakout = true;          // Notify on breakout
input bool            InpNotifyOnClose = true;             // Notify on trade close
input bool            InpNotifyOnRisk = true;              // Notify on risk events
input bool            InpDetailedLogging = true;           // Enable detailed logging

//--- General Settings
input group "=== General Settings ===="
input ulong           InpMagicNumber = 888999;             // Magic number
input string          InpTradeComment = "LondonBO_Pro";     // Trade comment

//--- Global Variables
ulong MagicNumber = 888999;

//--- Indicator Handles
int h_atr, h_atr_slow, h_volume;

//--- Risk Management Variables
double dailyStartBalance, weeklyStartBalance, peakEquity;
bool   dailyLossLimitHit = false, weeklyLossLimitHit = false;
bool   drawdownLimitHit = false, equityProtectionHit = false;
int    consecutiveLosses = 0;
datetime lastTradeTime = 0;
datetime lastDayCheck = 0, lastWeekCheck = 0;
datetime currentDay = 0;

//--- Asian Range Variables
struct AsianRange
{
    datetime startTime;
    datetime endTime;
    double   highPrice;
    double   lowPrice;
    double   rangePips;
    bool     isValid;
    bool     breakoutOccurred;
};
AsianRange currentRange;

//--- Position Management
struct AdvancedPosition
{
    ulong    ticket;
    datetime openTime;
    double   entryPrice;
    double   stopLoss;
    double   takeProfit;
    double   riskAmount;
    double   originalSL;
    bool     breakevenApplied;
    bool     trailingActive;
    bool     partial1Closed;
    bool     partial2Closed;
    ENUM_POSITION_TYPE type;
    double   highestProfit;
    double   lowestProfit;
};
AdvancedPosition managedPositions[];

//--- Market Analysis Variables
double currentATR = 0;
double averageATR = 0;
double volatilityRatio = 0;
double currentSpread = 0;
double liquidityScore = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize magic number
    MagicNumber = InpMagicNumber;
    
    // Initialize indicators
    h_atr = iATR(_Symbol, PERIOD_CURRENT, InpATRPeriod);
    h_atr_slow = iMA(_Symbol, PERIOD_CURRENT, InpATRAveragePeriod, 0, MODE_SMA, h_atr);
    h_volume = iVolumes(_Symbol, PERIOD_CURRENT, VOLUME_TICK);
    
    if(h_atr == INVALID_HANDLE || h_atr_slow == INVALID_HANDLE || h_volume == INVALID_HANDLE)
    {
        Print("Error creating indicators");
        return INIT_FAILED;
    }
    
    // Initialize risk management
    dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    weeklyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    peakEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    
    // Initialize Asian range
    ResetAsianRange();
    
    Print("=== London Breakout Pro v1.0 Initialized ===");
    Print("Optimized for: ", _Symbol);
    Print("Risk per trade: ", InpRiskPercentage, "%");
    Print("Asian session: ", InpAsianStartHour, ":00 - ", InpAsianEndHour, ":00 GMT");
    Print("London session: ", InpLondonStartHour, ":00 - ", InpLondonEndHour, ":00 GMT");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    IndicatorRelease(h_atr);
    IndicatorRelease(h_atr_slow);
    IndicatorRelease(h_volume);
    
    Print("London Breakout Pro v1.0 Deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Update market analysis
    UpdateMarketAnalysis();
    
    // Check for new opportunities on new bar
    static datetime lastBarTime = 0;
    datetime newBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(newBarTime > lastBarTime)
    {
        lastBarTime = newBarTime;
        
        // Daily/weekly checks
        CheckNewDay();
        CheckNewWeek();
        
        // Asian range detection
        UpdateAsianRange();
        
        // London breakout logic
        CheckLondonBreakout();
    }
}

//+------------------------------------------------------------------+
//| Update market analysis                                           |
//+------------------------------------------------------------------+
void UpdateMarketAnalysis()
{
    // Update ATR and volatility
    double atr_buffer[1], atr_slow_buffer[1];
    if(CopyBuffer(h_atr, 0, 0, 1, atr_buffer) > 0)
        currentATR = atr_buffer[0];
    
    if(CopyBuffer(h_atr_slow, 0, 0, 1, atr_slow_buffer) > 0)
        averageATR = atr_slow_buffer[0];
    
    volatilityRatio = (averageATR > 0) ? currentATR / averageATR : 1.0;
    
    // Update spread
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    currentSpread = (ask - bid) / (point * 10); // Convert to pips
    
    // Calculate liquidity score (simplified)
    liquidityScore = CalculateLiquidityScore();
}

//+------------------------------------------------------------------+
//| Calculate liquidity score                                       |
//+------------------------------------------------------------------+
double CalculateLiquidityScore()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int hour = dt.hour;
    
    double score = 0.0;
    
    // Time-based liquidity
    if(hour >= 7 && hour <= 17) score += 0.4;      // London hours
    if(hour >= 13 && hour <= 22) score += 0.3;     // NY hours
    if(hour >= 8 && hour <= 16) score += 0.2;      // Overlap
    
    // Volatility component
    if(volatilityRatio >= 1.0 && volatilityRatio <= 2.0) score += 0.1;
    
    return MathMin(score, 1.0);
}

//+------------------------------------------------------------------+
//| Reset Asian range                                               |
//+------------------------------------------------------------------+
void ResetAsianRange()
{
    currentRange.startTime = 0;
    currentRange.endTime = 0;
    currentRange.highPrice = 0;
    currentRange.lowPrice = 999999;
    currentRange.rangePips = 0;
    currentRange.isValid = false;
    currentRange.breakoutOccurred = false;
}

//+------------------------------------------------------------------+
//| Update Asian range detection                                    |
//+------------------------------------------------------------------+
void UpdateAsianRange()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int currentHour = dt.hour;
    
    // Check if we're in Asian session
    bool inAsianSession = IsAsianSession(currentHour);
    
    if(inAsianSession)
    {
        // Start new range if needed
        if(currentRange.startTime == 0)
        {
            currentRange.startTime = TimeCurrent();
            currentRange.highPrice = iHigh(_Symbol, PERIOD_CURRENT, 0);
            currentRange.lowPrice = iLow(_Symbol, PERIOD_CURRENT, 0);
        }
        
        // Update range boundaries
        double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
        double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);
        
        if(currentHigh > currentRange.highPrice) currentRange.highPrice = currentHigh;
        if(currentLow < currentRange.lowPrice) currentRange.lowPrice = currentLow;
    }
    else if(currentRange.startTime > 0 && !currentRange.isValid)
    {
        // Asian session ended, validate range
        ValidateAsianRange();
    }
}

//+------------------------------------------------------------------+
//| Check if current hour is in Asian session                       |
//+------------------------------------------------------------------+
bool IsAsianSession(int hour)
{
    if(InpAsianStartHour < InpAsianEndHour)
        return (hour >= InpAsianStartHour && hour < InpAsianEndHour);
    else
        return (hour >= InpAsianStartHour || hour < InpAsianEndHour);
}

//+------------------------------------------------------------------+
//| Validate Asian range                                            |
//+------------------------------------------------------------------+
void ValidateAsianRange()
{
    if(currentRange.startTime == 0) return;
    
    currentRange.endTime = TimeCurrent();
    
    // Calculate range in pips
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    currentRange.rangePips = (currentRange.highPrice - currentRange.lowPrice) / (point * 10);
    
    // Validate range size
    if(currentRange.rangePips >= InpMinRangePips && currentRange.rangePips <= InpMaxRangePips)
    {
        currentRange.isValid = true;
        Print("Valid Asian Range: ", currentRange.rangePips, " pips");
    }
    else
    {
        Print("Asian range invalid - Size: ", currentRange.rangePips, " pips");
        ResetAsianRange();
    }
}

//+------------------------------------------------------------------+
//| Check for London breakout                                       |
//+------------------------------------------------------------------+
void CheckLondonBreakout()
{
    if(!currentRange.isValid || currentRange.breakoutOccurred) return;
    if(!IsRiskManaged()) return;
    
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int currentHour = dt.hour;
    
    // Check if we're in London session
    if(currentHour < InpLondonStartHour || currentHour >= InpLondonEndHour) return;
    
    // Check market conditions
    if(!AreMarketConditionsFavorable()) return;
    
    // Get current price
    double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 0);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double bufferPips = InpBreakoutBuffer * point * 10;
    
    // Check for breakouts
    bool bullishBreakout = currentPrice > (currentRange.highPrice + bufferPips);
    bool bearishBreakout = currentPrice < (currentRange.lowPrice - bufferPips);
    
    if(bullishBreakout && InpAllowBothDirections)
    {
        if(IsVolumeConfirmed() && CanOpenPosition())
        {
            ExecuteBreakoutTrade(ORDER_TYPE_BUY);
            currentRange.breakoutOccurred = true;
        }
    }
    else if(bearishBreakout && InpAllowBothDirections)
    {
        if(IsVolumeConfirmed() && CanOpenPosition())
        {
            ExecuteBreakoutTrade(ORDER_TYPE_SELL);
            currentRange.breakoutOccurred = true;
        }
    }
}

//+------------------------------------------------------------------+
//| Check if market conditions are favorable                        |
//+------------------------------------------------------------------+
bool AreMarketConditionsFavorable()
{
    // Volatility filter
    if(InpUseVolatilityFilter)
    {
        if(volatilityRatio < InpMinVolatilityRatio || volatilityRatio > InpMaxVolatilityRatio)
            return false;
    }
    
    // Spread filter
    if(InpUseSpreadFilter)
    {
        if(currentSpread > InpMaxSpreadPips)
            return false;
    }
    
    // Liquidity filter
    if(InpUseLiquidityFilter)
    {
        if(liquidityScore < InpMinLiquidityScore)
            return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check volume confirmation                                       |
//+------------------------------------------------------------------+
bool IsVolumeConfirmed()
{
    if(!InpRequireVolumeConfirmation) return true;
    
    double volumes[];
    ArraySetAsSeries(volumes, true);
    
    if(CopyBuffer(h_volume, 0, 0, InpVolumeLookback + 1, volumes) < InpVolumeLookback + 1)
        return false;
    
    // Calculate average volume
    double totalVolume = 0;
    for(int i = 1; i <= InpVolumeLookback; i++)
        totalVolume += volumes[i];
    
    double avgVolume = totalVolume / InpVolumeLookback;
    
    return volumes[0] >= (avgVolume * InpVolumeMultiplier);
}

//+------------------------------------------------------------------+
//| Check if can open new position                                  |
//+------------------------------------------------------------------+
bool CanOpenPosition()
{
    // Check concurrent trades limit
    if(PositionsTotal() >= InpMaxConcurrentTrades) return false;
    
    // Check cooldown period
    if(lastTradeTime > 0)
    {
        if((TimeCurrent() - lastTradeTime) < (InpCooldownMinutes * 60))
            return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Execute breakout trade                                          |
//+------------------------------------------------------------------+
void ExecuteBreakoutTrade(ENUM_ORDER_TYPE orderType)
{
    double price = (orderType == ORDER_TYPE_BUY) ? 
        SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    // Calculate stop loss
    double sl = CalculateStopLoss(orderType);
    if(sl == 0) return;
    
    // Calculate take profit
    double tp = CalculateTakeProfit(orderType, price, sl);
    
    // Validate risk-reward
    double risk = MathAbs(price - sl);
    double reward = MathAbs(tp - price);
    double rr = (risk > 0) ? reward / risk : 0;
    
    if(rr < InpMinRiskReward)
    {
        Print("Trade rejected - R:R too low: ", rr);
        return;
    }
    
    // Calculate position size
    double lots = CalculatePositionSize(risk);
    if(lots <= 0) return;
    
    // Execute trade
    MqlTradeRequest request = {};
    MqlTradeResult result_info = {};
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lots;
    request.type = orderType;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = MagicNumber;
    request.comment = InpTradeComment;
    
    bool result = OrderSend(request, result_info);
    if(!result) Print("Trade execution failed: ", result_info.retcode);
    
    if(result)
    {
        lastTradeTime = TimeCurrent();
        Print("London Breakout Trade executed: ", EnumToString(orderType), " at ", price, " SL: ", sl, " TP: ", tp, " R:R: ", rr);
    }
}

//+------------------------------------------------------------------+
//| Calculate stop loss                                             |
//+------------------------------------------------------------------+
double CalculateStopLoss(ENUM_ORDER_TYPE orderType)
{
    double sl = 0;
    double atrBuffer = currentATR * 0.5;
    
    if(orderType == ORDER_TYPE_BUY)
    {
        // Place SL below Asian range low with ATR buffer
        sl = currentRange.lowPrice - atrBuffer;
    }
    else
    {
        // Place SL above Asian range high with ATR buffer  
        sl = currentRange.highPrice + atrBuffer;
    }
    
    return NormalizeDouble(sl, _Digits);
}

//+------------------------------------------------------------------+
//| Calculate take profit                                           |
//+------------------------------------------------------------------+
double CalculateTakeProfit(ENUM_ORDER_TYPE orderType, double price, double sl)
{
    double risk = MathAbs(price - sl);
    double baseTarget = risk * InpBaseRiskReward;
    
    double tp = 0;
    if(InpUseDynamicTP)
    {
        // Use ATR-based dynamic target
        double atrTarget = currentATR * 2.0;
        double dynamicReward = MathMax(baseTarget, atrTarget);
        
        // Cap at maximum R:R
        double maxReward = risk * InpMaxRiskReward;
        dynamicReward = MathMin(dynamicReward, maxReward);
        
        tp = (orderType == ORDER_TYPE_BUY) ? price + dynamicReward : price - dynamicReward;
    }
    else
    {
        tp = (orderType == ORDER_TYPE_BUY) ? price + baseTarget : price - baseTarget;
    }
    
    return NormalizeDouble(tp, _Digits);
}

//+------------------------------------------------------------------+
//| Calculate position size with risk management                    |
//+------------------------------------------------------------------+
double CalculatePositionSize(double risk)
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = balance * (InpRiskPercentage / 100.0);
    
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    double lots = 0;
    if(tickValue > 0 && tickSize > 0)
    {
        double riskPerLot = (risk / tickSize) * tickValue;
        if(riskPerLot > 0) lots = riskAmount / riskPerLot;
    }
    
    // Apply position size limit
    double maxPositionValue = balance * (InpMaxPositionSize / 100.0);
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double contractSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_CONTRACT_SIZE);
    double maxLots = maxPositionValue / (currentPrice * contractSize);
    
    if(lots > maxLots) lots = maxLots;
    
    // Normalize lot size
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lots = MathRound(lots / lotStep) * lotStep;
    
    if(lots < minLot) lots = minLot;
    if(lots > maxLot) lots = maxLot;
    
    return lots;
}

//+------------------------------------------------------------------+
//| Advanced risk management checks                                 |
//+------------------------------------------------------------------+
bool IsRiskManaged()
{
    // Daily loss limit
    if(dailyLossLimitHit) return false;
    
    // Weekly loss limit  
    if(weeklyLossLimitHit) return false;
    
    // Drawdown protection
    if(drawdownLimitHit) return false;
    
    // Equity protection
    if(equityProtectionHit) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check new day                                                   |
//+------------------------------------------------------------------+
void CheckNewDay()
{
    datetime newDay = iTime(_Symbol, PERIOD_D1, 0);
    if(newDay > lastDayCheck)
    {
        dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        dailyLossLimitHit = false;
        lastDayCheck = newDay;
        currentDay = newDay;
        
        // Reset Asian range for new day
        ResetAsianRange();
        
        Print("New trading day - Daily limits reset");
    }
}

//+------------------------------------------------------------------+
//| Check new week                                                  |
//+------------------------------------------------------------------+
void CheckNewWeek()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    if(dt.day_of_week == 1 && lastWeekCheck < currentDay) // Monday
    {
        weeklyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        weeklyLossLimitHit = false;
        drawdownLimitHit = false;
        equityProtectionHit = false;
        lastWeekCheck = TimeCurrent();
        
        Print("New trading week - Weekly limits reset");
    }
}