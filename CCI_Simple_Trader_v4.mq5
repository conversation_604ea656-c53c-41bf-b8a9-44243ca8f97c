//+------------------------------------------------------------------+
//|                        CCI_Simple_Trader_v4.mq5                 |
//|                      Copyright 2025, Simple Trading Systems     |
//|                         Simplified for Trade Generation          |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Simple Trading Systems"
#property link      "https://www.simpletrading.com"
#property version   "4.00"

#include <Trade/Trade.mqh>

//--- Input Parameters
//--- Core Strategy Settings
input group "=== Core CCI Strategy Settings ===="
input int             InpCCI_Period = 20;                  // CCI Period
input int             InpATR_Period = 14;                  // ATR Period
input int             InpEMA_Period = 50;                  // EMA Period for Trend
input int             InpSwingLookback = 20;               // Swing High/Low Lookback
input double          InpCCI_EntryLevel = 100;             // CCI Entry Level
input double          InpCCI_ExitLevel = 0;                // CCI Exit Level

//--- Risk Management
input group "=== Risk Management ===="
input double          InpRiskPercentage = 0.5;             // Risk per trade
input double          InpMaxDailyLoss = 3.0;               // Max daily loss
input int             InpMaxConcurrentTrades = 1;          // Max concurrent trades
input double          InpMinRiskReward = 1.5;              // Minimum R:R ratio
input double          InpMaxPositionSize = 10.0;           // Max position size (% of account)

//--- Exit Settings
input group "=== Exit Settings ===="
input double          InpFixedRiskReward = 2.0;            // Fixed Risk:Reward
input bool            InpUseBreakeven = true;              // Enable Breakeven
input double          InpBreakevenRR = 1.0;                // R:R to move to breakeven
input bool            InpUseTrailingStop = true;           // Enable Trailing Stop
input double          InpTrailStartRR = 1.5;               // R:R to start trailing

//--- Optional Filters (can be disabled)
input group "=== Optional Filters ===="
input bool            InpUseTrendFilter = true;            // Use EMA Trend Filter
input bool            InpUseSessionFilter = true;          // Trade only London/NY sessions

//--- General Settings
input group "=== General Settings ===="
input ulong           InpMagicNumber = 123456;            // Magic Number
input string          InpTradeComment = "CCI_Simple_v4";   // Trade Comment

//--- Global Variables
int h_cci, h_atr, h_ema;
CTrade trade;

//--- Risk Management Variables
double dailyStartBalance;
bool   dailyLossLimitReached = false;
datetime lastDayCheck = 0;

//--- Position Management
struct PositionState
{
   ulong   ticket;
   double  entryPrice;
   double  stopLoss;
   bool    breakevenApplied;
};
PositionState managedPositions[];

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetTypeFillingBySymbol(_Symbol);

    // Initialize indicators
    h_cci = iCCI(_Symbol, PERIOD_CURRENT, InpCCI_Period, PRICE_TYPICAL);
    h_atr = iATR(_Symbol, PERIOD_CURRENT, InpATR_Period);
    h_ema = iMA(_Symbol, PERIOD_CURRENT, InpEMA_Period, 0, MODE_EMA, PRICE_CLOSE);

    if(h_cci == INVALID_HANDLE || h_atr == INVALID_HANDLE || h_ema == INVALID_HANDLE)
    {
        Print("Error creating indicators");
        return INIT_FAILED;
    }

    dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    
    Print("=== CCI Simple Trader v4.0 Initialized ===");
    Print("Risk per trade: ", InpRiskPercentage, "%");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    IndicatorRelease(h_cci);
    IndicatorRelease(h_atr);
    IndicatorRelease(h_ema);
    Print("CCI Simple Trader v4.0 Deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    ManagePositions();

    static datetime lastBarTime = 0;
    datetime newBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);

    if(newBarTime > lastBarTime)
    {
        lastBarTime = newBarTime;
        
        CheckNewDay();
        CheckForNewTrade();
    }
}

//+------------------------------------------------------------------+
//| Check for new trade opportunities - SIMPLIFIED                   |
//+------------------------------------------------------------------+
void CheckForNewTrade()
{
    // Basic risk checks
    if(dailyLossLimitReached) return;
    if(PositionsTotal() >= InpMaxConcurrentTrades) return;
    
    // Session filter (optional)
    if(InpUseSessionFilter && !IsGoodSession()) return;

    // Get CCI values
    double cci[];
    ArraySetAsSeries(cci, true);
    if(CopyBuffer(h_cci, 0, 0, 3, cci) < 3) return;

    // Get EMA value for trend (optional)
    bool trendOK = true;
    if(InpUseTrendFilter)
    {
        double ema[1];
        if(CopyBuffer(h_ema, 0, 0, 1, ema) < 1) return;
        double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 0);
        // We'll use this in the signal logic below
    }

    // Simple CCI cross signals
    int signal = 0;
    
    // Bullish: CCI was below -EntryLevel, now crossing back above
    if(cci[2] <= -InpCCI_EntryLevel && cci[1] > -InpCCI_EntryLevel && cci[0] > cci[1])
    {
        signal = 1; // Buy signal
    }
    // Bearish: CCI was above +EntryLevel, now crossing back below  
    else if(cci[2] >= InpCCI_EntryLevel && cci[1] < InpCCI_EntryLevel && cci[0] < cci[1])
    {
        signal = -1; // Sell signal
    }

    // Trend filter (if enabled)
    if(signal != 0 && InpUseTrendFilter)
    {
        double ema[1];
        CopyBuffer(h_ema, 0, 0, 1, ema);
        double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 0);
        
        // Only trade with the trend
        if(signal == 1 && currentPrice < ema[0]) signal = 0; // No buy below EMA
        if(signal == -1 && currentPrice > ema[0]) signal = 0; // No sell above EMA
    }

    // Execute trade
    if(signal == 1)
        ExecuteTrade(ORDER_TYPE_BUY);
    else if(signal == -1)
        ExecuteTrade(ORDER_TYPE_SELL);
}

//+------------------------------------------------------------------+
//| Check if current session is good for trading                    |
//+------------------------------------------------------------------+
bool IsGoodSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int hour = dt.hour;
    
    // London: 8-17 GMT, NY: 13-22 GMT
    return (hour >= 8 && hour < 17) || (hour >= 13 && hour < 22);
}

//+------------------------------------------------------------------+
//| Execute a trade with simple logic                               |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE orderType)
{
    double price = (orderType == ORDER_TYPE_BUY) ? 
        SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    // Calculate stop loss using swing high/low
    double sl = CalculateStopLoss(orderType, price);
    if(sl == 0) return;
    
    // Calculate take profit
    double risk = MathAbs(price - sl);
    double tp = (orderType == ORDER_TYPE_BUY) ? 
        price + (risk * InpFixedRiskReward) : price - (risk * InpFixedRiskReward);
    
    // Check minimum R:R
    double actualRR = MathAbs(tp - price) / risk;
    if(actualRR < InpMinRiskReward) return;
    
    // Calculate position size
    double lots = CalculatePositionSize(risk);
    if(lots <= 0) return;
    
    // Execute trade
    bool result = false;
    if(orderType == ORDER_TYPE_BUY)
        result = trade.Buy(lots, _Symbol, price, sl, tp, InpTradeComment);
    else
        result = trade.Sell(lots, _Symbol, price, sl, tp, InpTradeComment);
    
    if(result)
    {
        Print("Trade opened: ", EnumToString(orderType), " at ", price, " SL:", sl, " TP:", tp);
    }
}

//+------------------------------------------------------------------+
//| Calculate stop loss using swing high/low                        |
//+------------------------------------------------------------------+
double CalculateStopLoss(ENUM_ORDER_TYPE orderType, double price)
{
    double atr[1];
    if(CopyBuffer(h_atr, 0, 0, 1, atr) < 1) return 0;
    
    double buffer = atr[0] * 0.5;
    double sl = 0;
    
    if(orderType == ORDER_TYPE_BUY)
    {
        double lowestLow = iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, InpSwingLookback, 1));
        sl = lowestLow - buffer;
    }
    else
    {
        double highestHigh = iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, InpSwingLookback, 1));
        sl = highestHigh + buffer;
    }
    
    return NormalizeDouble(sl, _Digits);
}

//+------------------------------------------------------------------+
//| Calculate position size                                         |
//+------------------------------------------------------------------+
double CalculatePositionSize(double risk)
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = balance * (InpRiskPercentage / 100.0);
    
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    double lots = 0;
    if(tickValue > 0 && tickSize > 0)
    {
        double riskPerLot = (risk / tickSize) * tickValue;
        if(riskPerLot > 0) lots = riskAmount / riskPerLot;
    }
    
    // Apply position size limit
    double maxPositionValue = balance * (InpMaxPositionSize / 100.0);
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double contractSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_CONTRACT_SIZE);
    double maxLots = maxPositionValue / (currentPrice * contractSize);
    
    if(lots > maxLots) lots = maxLots;
    
    // Normalize to lot step
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lots = MathRound(lots / lotStep) * lotStep;
    
    if(lots < minLot) lots = minLot;
    if(lots > maxLot) lots = maxLot;
    
    return lots;
}

//+------------------------------------------------------------------+
//| Manage positions (breakeven, trailing)                         |
//+------------------------------------------------------------------+
void ManagePositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetInteger(POSITION_MAGIC) != InpMagicNumber) continue;
        if(PositionGetString(POSITION_SYMBOL) != _Symbol) continue;
        
        double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        double currentSL = PositionGetDouble(POSITION_SL);
        ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        
        double currentPrice = (posType == POSITION_TYPE_BUY) ?
            SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        
        double risk = MathAbs(openPrice - currentSL);
        double currentProfit = (posType == POSITION_TYPE_BUY) ?
            (currentPrice - openPrice) : (openPrice - currentPrice);
        
        double currentRR = (risk > 0) ? currentProfit / risk : 0;
        
        // Find position in managed array
        int posIndex = -1;
        for(int j = 0; j < ArraySize(managedPositions); j++)
        {
            if(managedPositions[j].ticket == ticket)
            {
                posIndex = j;
                break;
            }
        }
        
        // Breakeven management
        if(InpUseBreakeven && posIndex >= 0 && !managedPositions[posIndex].breakevenApplied && 
           currentRR >= InpBreakevenRR)
        {
            double newSL = openPrice;
            if(trade.PositionModify(ticket, newSL, PositionGetDouble(POSITION_TP)))
            {
                managedPositions[posIndex].breakevenApplied = true;
                Print("Breakeven applied to position ", ticket);
            }
        }
        
        // Simple trailing stop
        if(InpUseTrailingStop && currentRR >= InpTrailStartRR)
        {
            double trailDistance = risk * 0.5; // Trail at half the original risk
            double newSL = currentPrice - ((posType == POSITION_TYPE_BUY ? 1 : -1) * trailDistance);
            
            bool shouldModify = (posType == POSITION_TYPE_BUY && newSL > currentSL) ||
                               (posType == POSITION_TYPE_SELL && newSL < currentSL);
            
            if(shouldModify)
            {
                trade.PositionModify(ticket, NormalizeDouble(newSL, _Digits), PositionGetDouble(POSITION_TP));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Handle trade transactions                                       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                        const MqlTradeRequest& request,
                        const MqlTradeResult& result)
{
    if(trans.type != TRADE_TRANSACTION_DEAL_ADD) return;
    if(!HistoryDealSelect(trans.deal)) return;
    if(HistoryDealGetInteger(trans.deal, DEAL_MAGIC) != InpMagicNumber) return;

    long dealEntry = HistoryDealGetInteger(trans.deal, DEAL_ENTRY);
    ulong positionId = HistoryDealGetInteger(trans.deal, DEAL_POSITION_ID);

    if(dealEntry == DEAL_ENTRY_IN)
    {
        // New position opened
        PositionState newPos;
        newPos.ticket = positionId;
        newPos.entryPrice = HistoryDealGetDouble(trans.deal, DEAL_PRICE);
        newPos.breakevenApplied = false;

        int size = ArraySize(managedPositions);
        ArrayResize(managedPositions, size + 1);
        managedPositions[size] = newPos;
    }
    else if(dealEntry == DEAL_ENTRY_OUT)
    {
        // Position closed
        if(!PositionSelectByTicket(positionId))
        {
            double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
            Print("Trade closed with profit: ", profit);
            
            // Check daily loss limit
            CheckDailyLoss();
            
            // Remove from managed positions
            for(int i = ArraySize(managedPositions) - 1; i >= 0; i--)
            {
                if(managedPositions[i].ticket == positionId)
                {
                    ArrayRemove(managedPositions, i, 1);
                    break;
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check daily loss limit                                          |
//+------------------------------------------------------------------+
void CheckDailyLoss()
{
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double dailyLoss = (dailyStartBalance - currentBalance) / dailyStartBalance * 100.0;
    
    if(dailyLoss >= InpMaxDailyLoss)
    {
        dailyLossLimitReached = true;
        Print("Daily loss limit reached: ", dailyLoss, "%");
    }
}

//+------------------------------------------------------------------+
//| Reset daily counters                                            |
//+------------------------------------------------------------------+
void CheckNewDay()
{
    datetime currentDay = iTime(_Symbol, PERIOD_D1, 0);
    if(currentDay > lastDayCheck)
    {
        dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        dailyLossLimitReached = false;
        lastDayCheck = currentDay;
        
        Print("New trading day - limits reset");
    }
}