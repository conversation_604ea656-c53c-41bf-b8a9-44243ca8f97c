//+------------------------------------------------------------------+
//|                          London_Breakout_Minimal.mq5           |
//|                     Minimal London Breakout for Testing        |
//+------------------------------------------------------------------+
#property copyright "Testing Version"
#property version   "1.00"

//--- Input Parameters
input group "=== London Breakout Settings ===="
input int    InpLondonStartHour = 6;     // London session start (GMT)
input int    InpLondonEndHour = 9;       // London session end (GMT)
input double InpBreakoutBuffer = 3.0;    // Breakout buffer (pips)
input double InpMinRiskReward = 1.8;     // Minimum R:R ratio
input int    InpMinRangePips = 3;        // Minimum range size (pips)
input int    InpMaxRangePips = 120;      // Maximum range size (pips)

input group "=== Risk Settings ===="
input double InpRiskPercentage = 1.0;    // Risk per trade (%)
input ulong  InpMagicNumber = 888999;    // Magic number

//--- Global Variables
struct SimpleRange
{
    double high;
    double low;
    double rangePips;
    bool   isValid;
    bool   breakoutOccurred;
};
SimpleRange asianRange;

datetime lastRangeTime = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== London Breakout Minimal Started ===");
    ResetRange();
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    static datetime lastBarTime = 0;
    datetime newBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(newBarTime > lastBarTime)
    {
        lastBarTime = newBarTime;
        
        MqlDateTime dt;
        TimeToStruct(TimeCurrent(), dt);
        int currentHour = dt.hour;
        
        // Asian session: 23:00-6:00 GMT
        if((currentHour >= 23 || currentHour < 6) && !asianRange.isValid)
        {
            UpdateAsianRange();
        }
        
        // Validate range when Asian session ends
        if(currentHour == 6 && !asianRange.isValid && lastRangeTime != newBarTime)
        {
            ValidateRange();
            lastRangeTime = newBarTime;
        }
        
        // London breakout: 6:00-9:00 GMT
        if(currentHour >= InpLondonStartHour && currentHour < InpLondonEndHour)
        {
            CheckBreakout();
        }
        
        // Reset for new day
        if(currentHour == 23)
        {
            ResetRange();
        }
    }
}

//+------------------------------------------------------------------+
//| Reset Asian range                                               |
//+------------------------------------------------------------------+
void ResetRange()
{
    asianRange.high = 0;
    asianRange.low = 999999;
    asianRange.rangePips = 0;
    asianRange.isValid = false;
    asianRange.breakoutOccurred = false;
    Print("Range reset for new session");
}

//+------------------------------------------------------------------+
//| Update Asian range during session                               |
//+------------------------------------------------------------------+
void UpdateAsianRange()
{
    double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
    double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);
    
    if(currentHigh > asianRange.high) asianRange.high = currentHigh;
    if(currentLow < asianRange.low) asianRange.low = currentLow;
}

//+------------------------------------------------------------------+
//| Validate Asian range                                            |
//+------------------------------------------------------------------+
void ValidateRange()
{
    if(asianRange.high == 0 || asianRange.low == 999999) return;
    
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    asianRange.rangePips = (asianRange.high - asianRange.low) / (point * 10);
    
    if(asianRange.rangePips >= InpMinRangePips && asianRange.rangePips <= InpMaxRangePips)
    {
        asianRange.isValid = true;
        Print("Valid Asian Range: ", asianRange.rangePips, " pips (", asianRange.high, " - ", asianRange.low, ")");
    }
    else
    {
        Print("Invalid range: ", asianRange.rangePips, " pips - outside ", InpMinRangePips, "-", InpMaxRangePips, " pip range");
        ResetRange();
    }
}

//+------------------------------------------------------------------+
//| Check for London breakout                                       |
//+------------------------------------------------------------------+
void CheckBreakout()
{
    if(!asianRange.isValid || asianRange.breakoutOccurred) return;
    
    double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 0);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double buffer = InpBreakoutBuffer * point * 10;
    
    // Check for breakouts
    if(currentPrice > (asianRange.high + buffer))
    {
        ExecuteTrade(ORDER_TYPE_BUY, currentPrice);
        asianRange.breakoutOccurred = true;
        Print("BULLISH BREAKOUT at ", currentPrice, " (Range high: ", asianRange.high, ")");
    }
    else if(currentPrice < (asianRange.low - buffer))
    {
        ExecuteTrade(ORDER_TYPE_SELL, currentPrice);
        asianRange.breakoutOccurred = true;
        Print("BEARISH BREAKOUT at ", currentPrice, " (Range low: ", asianRange.low, ")");
    }
}

//+------------------------------------------------------------------+
//| Execute trade                                                    |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE orderType, double price)
{
    // Simple stop loss calculation
    double sl = 0;
    if(orderType == ORDER_TYPE_BUY)
        sl = asianRange.low - (10 * SymbolInfoDouble(_Symbol, SYMBOL_POINT));
    else
        sl = asianRange.high + (10 * SymbolInfoDouble(_Symbol, SYMBOL_POINT));
    
    // Calculate take profit based on R:R
    double risk = MathAbs(price - sl);
    double reward = risk * InpMinRiskReward;
    double tp = (orderType == ORDER_TYPE_BUY) ? price + reward : price - reward;
    
    // Simple position size (0.01 lots for testing)
    double lots = 0.01;
    
    // Execute trade
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lots;
    request.type = orderType;
    request.price = price;
    request.sl = NormalizeDouble(sl, _Digits);
    request.tp = NormalizeDouble(tp, _Digits);
    request.magic = InpMagicNumber;
    request.comment = "LB_Minimal";
    
    if(OrderSend(request, result))
    {
        Print("TRADE EXECUTED: ", EnumToString(orderType), " at ", price, 
              " SL:", sl, " TP:", tp, " Risk:", risk, " Reward:", reward);
    }
    else
    {
        Print("TRADE FAILED: Error ", result.retcode);
    }
}