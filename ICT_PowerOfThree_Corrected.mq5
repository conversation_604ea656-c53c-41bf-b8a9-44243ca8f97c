//+------------------------------------------------------------------+
//|                                     ICT_PowerOfThree_Corrected.mq5 |
//|                        Copyright 2025, Gemini Trading Systems      |
//|                  (Full Suite + Robust Time Handling - Final)       |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Gemini Trading Systems (Final)"
#property link      "https://www.google.com"
#property version   "4.00" // Robust Time Handling

#include <Trade/Trade.mqh>

//--- Input parameters
input group "=== ICT Power of Three Settings ==="
input string InpAsianSession = "20:00-00:00";      // Asian Session for Accumulation (GMT/UTC HH:MM-HH:MM)
input string InpLondonSession = "07:00-10:00";     // London Session for Manipulation (GMT/UTC HH:MM-HH:MM)
input string InpNYSession = "12:00-15:00";         // NY Session for Distribution (GMT/UTC HH:MM-HH:MM)
input double InpMaxAsianRangePips = 50.0;          // Max size of Asian Range to be valid
input double InpManipulationMinPips = 5.0;         // Min pips for a valid Judas Swing

input group "=== Risk Management ==="
input double InpRiskPercentage = 1.0;              // Risk per trade (%)
input int    InpMaxConcurrentTrades = 1;           // Max concurrent trades
input double InpMaxDailyLossPercent = 5.0;         // Max daily loss limit (%)
input int    InpMaxConsecutiveLosses = 3;          // Max consecutive losses allowed
input double InpMaxSpreadPips = 2.0;               // Max allowed spread in pips
input uint   InpMaxSlippagePoints = 10;            // Max allowed slippage in points

input group "=== Exit & Trailing Stop Settings ==="
input double InpFixedRiskReward = 2.5;             // Fixed Risk:Reward Ratio (initial TP)
input bool   InpUseDynamicTP = true;               // Enable Dynamic Take Profit
enum   ENUM_TP_TARGET {
    PREV_DAY_HL,    // Previous Day's High/Low
    ASIAN_RANGE_OPPOSITE // Opposite side of Asian Range
};
input ENUM_TP_TARGET InpDynamicTPTarget = PREV_DAY_HL; // Dynamic TP Target
input double InpDynamicTPBufferPips = 5.0;         // Buffer in pips for dynamic TP
input double InpMinRiskReward = 1.5;               // Minimum R:R required for any trade

input double InpStopLossRangePercentage = 0.2;     // Stop Loss as percentage of Asian Range (e.g., 0.2 for 20%)
input bool   InpUseTrailingStop = true;            // Enable Trailing Stop
input double InpTrailStartPips = 20.0;             // Pips in profit to activate trailing SL
input bool   InpUseBreakeven = true;               // Enable Breakeven
input double InpBreakevenPips = 10.0;              // Pips in profit to move to breakeven
input double InpBreakevenBufferPips = 2.0;         // Buffer in pips for breakeven SL
input double InpTrailDistanceATR = 2.0;            // Trailing distance in ATR multiples
input int    InpTrailATRPeriod = 14;               // ATR Period for Trailing Stop
input bool   InpUsePartialTakeProfits = false;     // Enable Partial Take Profits
input double InpPartialTP1Ratio = 0.5;             // Ratio of initial TP for first partial TP
input double InpPartialTP1Volume = 0.5;            // Volume to close at first partial TP (e.g., 0.5 for 50%)
input double InpPartialTP2Ratio = 0.8;             // Ratio of initial TP for second partial TP
input double InpPartialTP2Volume = 0.3;            // Volume to close at second partial TP (e.g., 0.3 for 30%)

//--- Notification Settings
input group "=== Notification Settings ==="
input bool   InpEnableAlerts = true;            // Enable On-Screen Alerts
input bool   InpEnablePushNotifications = false; // Enable Push Notifications (to mobile)
input bool   InpEnableEmailNotifications = false; // Enable Email Notifications
input bool   InpNotifyOnTrade = true;           // Notify on Trade Open/Close
input bool   InpNotifyOnError = true;           // Notify on Trading Error/Risk

input group "=== General Settings ==="
input ulong  InpMagicNumber = 999999;
input string InpTradeComment = "ICT_PO3_Corrected";

//--- Global variables
CTrade trade;
int atr_handle;

//--- Session Times (Parsed from inputs - now GMT/UTC)
int asian_start_h, asian_start_m, asian_end_h, asian_end_m;
int london_start_h, london_start_m, london_end_h, london_end_m;
int ny_start_h, ny_start_m, ny_end_h, ny_end_m;

//--- PO3 State Machine
enum PO3_Phase { IDLE, ACCUMULATION_DEFINED, MANIPULATION_HIGH, MANIPULATION_LOW };
PO3_Phase currentPhase = IDLE;

//--- Accumulation Range Data
double asianRangeHigh = 0;
double asianRangeLow = 0;
double prevDayHigh = 0; // For dynamic TP
double prevDayLow = 0;  // For dynamic TP

//--- Manipulation Data
double manipulationHighPrice = 0;
double manipulationLowPrice = 0;

//--- Position State Tracking for Partial TPs
struct PositionState
{
   ulong   ticket;
   double  initial_volume;
   double  initial_sl;
   double  initial_tp;
   bool    partial1_taken;
   bool    partial2_taken;
   bool    breakeven_applied;
};
PositionState managedPositions[];

//--- Risk Management Globals
double   dailyStartBalance;
bool     dailyLossLimitHit = false;
int      consecutiveLosses = 0;
datetime lastTradeDay = 0;

//+------------------------------------------------------------------+
//| Send Notifications                                               |
//+------------------------------------------------------------------+
void SendNotification(string subject, string message)
{
    if(IsStopped()) return;
    if(InpEnableAlerts) Alert(subject, "\n", message);
    if(InpEnablePushNotifications)
    {
        string combined_message = subject + ": " + message;
        if(StringLen(combined_message) > 255) combined_message = StringSubstr(combined_message, 0, 255);
        SendNotification(combined_message);
    }
    if(InpEnableEmailNotifications) SendMail(subject, message);
}

//+------------------------------------------------------------------+
//| Parse Session Times from String Inputs                           |
//+------------------------------------------------------------------+
bool ParseSessionTimes()
{
    string parts[], start_parts[], end_parts[];

    // Asian Session
    if(StringSplit(InpAsianSession, '-', parts) != 2) return false;
    if(StringSplit(parts[0], ':', start_parts) != 2) return false;
    if(StringSplit(parts[1], ':', end_parts) != 2) return false;
    asian_start_h = (int)StringToInteger(start_parts[0]);
    asian_start_m = (int)StringToInteger(start_parts[1]);
    asian_end_h = (int)StringToInteger(end_parts[0]);
    asian_end_m = (int)StringToInteger(end_parts[1]);

    // London Session
    if(StringSplit(InpLondonSession, '-', parts) != 2) return false;
    if(StringSplit(parts[0], ':', start_parts) != 2) return false;
    if(StringSplit(parts[1], ':', end_parts) != 2) return false;
    london_start_h = (int)StringToInteger(start_parts[0]);
    london_start_m = (int)StringToInteger(start_parts[1]);
    london_end_h = (int)StringToInteger(end_parts[0]);
    london_end_m = (int)StringToInteger(end_parts[1]);
    
    // NY Session
    if(StringSplit(InpNYSession, '-', parts) != 2) return false;
    if(StringSplit(parts[0], ':', start_parts) != 2) return false;
    if(StringSplit(parts[1], ':', end_parts) != 2) return false;
    ny_start_h = (int)StringToInteger(start_parts[0]);
    ny_start_m = (int)StringToInteger(start_parts[1]);
    ny_end_h = (int)StringToInteger(end_parts[0]);
    ny_end_m = (int)StringToInteger(end_parts[1]);
    
    return true;
}

void UpdateDailyData(bool is_init); // Forward declaration

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    if(!ParseSessionTimes())
    {
        Print("Error: Could not parse session time inputs. Use format HH:MM-HH:MM.");
        return INIT_FAILED;
    }

    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetTypeFillingBySymbol(_Symbol);
    trade.SetDeviationInPoints(InpMaxSlippagePoints);
    
    atr_handle = iATR(_Symbol, PERIOD_M15, InpTrailATRPeriod);
    if(atr_handle == INVALID_HANDLE)
    {
        Print("Error creating ATR indicator for trailing stop.");
        return INIT_FAILED;
    }
    
    if (InpUsePartialTakeProfits && (InpPartialTP1Volume + InpPartialTP2Volume) >= 1.0)
    {
        Print("Error: Partial Take Profit volumes sum to 100% or more. Adjust InpPartialTP1Volume and InpPartialTP2Volume.");
        return INIT_FAILED;
    }

    UpdateDailyData(true); // Call once at start to set initial daily values and prev day H/L
    Print("=== ICT Power of Three Corrected EA Initialized (v5.00 - Dynamic TP) ===");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) { IndicatorRelease(atr_handle); Print("EA Deinitialized."); }

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Trailing Stop runs on every tick for precision
    ManageTrailingStops();

    //--- Entry logic runs only once per bar of the execution timeframe
    static datetime lastBarTime = 0;
    datetime newBarTime = (datetime)SeriesInfoInteger(_Symbol, PERIOD_M15, SERIES_LASTBAR_DATE);

    if(newBarTime > lastBarTime)
    {
        lastBarTime = newBarTime;
        
        CheckNewDay();
        
        if(!IsRiskManaged()) return;
        if(PositionsTotal() >= InpMaxConcurrentTrades) return;

        ManagePartialTakeProfits(); // Call the new partial take profit function

        MqlDateTime dt; TimeToStruct(TimeCurrent(), dt);
        int current_hour = dt.hour;
        int current_minute = dt.min;

        // --- PO3 State Machine Logic ---

        // Phase 1: Define Asian Range at the start of London Session
        if(current_hour == london_start_h && current_minute == london_start_m && currentPhase == IDLE)
        {
            DefineAccumulationRange();
        }

        // Phase 2: Check for Manipulation during London Session
        if(currentPhase == ACCUMULATION_DEFINED && current_hour >= london_start_h && current_hour < london_end_h)
        {
            CheckForManipulation();
        }

        // Phase 3: Check for Distribution during London or NY Session
        if((currentPhase == MANIPULATION_HIGH || currentPhase == MANIPULATION_LOW) && 
           ((current_hour >= london_start_h && current_hour < london_end_h) || 
            (current_hour >= ny_start_h && current_hour < ny_end_h)))
        {
            CheckForDistribution();
        }
    }
}

//+------------------------------------------------------------------+
//| 1. Define the Asian Session Accumulation Range                   |
//+------------------------------------------------------------------+
void DefineAccumulationRange()
{
    // Calculate the start and end time of the PREVIOUS Asian session
    datetime current_day_start = iTime(_Symbol, PERIOD_D1, 0);
    datetime prev_day_start = current_day_start - 24 * 3600; // Go back one day

    datetime asian_session_start_prev_day = prev_day_start + asian_start_h * 3600 + asian_start_m * 60;
    datetime asian_session_end_prev_day = prev_day_start + asian_end_h * 3600 + asian_end_m * 60;
    
    // Handle Asian session crossing midnight (e.g., 20:00 - 00:00)
    if (asian_end_h < asian_start_h)
    {
        asian_session_end_prev_day = current_day_start + asian_end_h * 3600 + asian_end_m * 60;
    }

    MqlRates rates[];
    int copied = CopyRates(_Symbol, PERIOD_M15, asian_session_start_prev_day, asian_session_end_prev_day, rates);
    
    if(copied <= 0)
    {
        Print("Could not copy rates for previous Asian session. Copied: ", copied);
        currentPhase = IDLE;
        return;
    }

    double range_high = 0;
    double range_low = 999999;
    for(int i = 0; i < copied; i++)
    {
        if(rates[i].high > range_high) range_high = rates[i].high;
        if(rates[i].low < range_low) range_low = rates[i].low;
    }

    asianRangeHigh = range_high;
    asianRangeLow = range_low;

    double range_pips = (asianRangeHigh - asianRangeLow) / (SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 10);

    if(range_pips > 0 && range_pips <= InpMaxAsianRangePips)
    {
        currentPhase = ACCUMULATION_DEFINED;
        SendNotification("PO3 Update", StringFormat("Asian Range defined: %.5f - %.5f (%.1f pips)", asianRangeHigh, asianRangeLow, range_pips));
    }
    else
    {
        currentPhase = IDLE;
        Print("Asian Range too wide (%.1f pips) or invalid. No setup today.", range_pips);
    }
}

//+------------------------------------------------------------------+
//| 2. Check for Manipulation (Judas Swing)                          |
//+------------------------------------------------------------------+
void CheckForManipulation()
{
    double currentHigh = iHigh(_Symbol, PERIOD_M15, 1);
    double currentLow = iLow(_Symbol, PERIOD_M15, 1);
    double min_break = InpManipulationMinPips * SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 10;

    // Check for manipulation above the high (Judas Swing Up)
    if(currentHigh > asianRangeHigh && (currentHigh - asianRangeHigh) >= min_break)
    {
        currentPhase = MANIPULATION_HIGH;
        manipulationHighPrice = currentHigh; // Store the manipulation high
        SendNotification("PO3 Update", StringFormat("Manipulation HIGH detected at %.5f. Looking for SELL entry.", currentHigh));
    }
    // Check for manipulation below the low (Judas Swing Down)
    else if(currentLow < asianRangeLow && (asianRangeLow - currentLow) >= min_break)
    {
        currentPhase = MANIPULATION_LOW;
        manipulationLowPrice = currentLow; // Store the manipulation low
        SendNotification("PO3 Update", StringFormat("Manipulation LOW detected at %.5f. Looking for BUY entry.", currentLow));
    }
}

//+------------------------------------------------------------------+
//| 3. Check for Distribution (Entry)                                |
//+------------------------------------------------------------------+
void CheckForDistribution()
{
    double currentClose = iClose(_Symbol, PERIOD_M15, 1);

    // After a high manipulation, look for price to close back inside the range for a SELL
    if(currentPhase == MANIPULATION_HIGH && currentClose < asianRangeHigh)
    {
        ExecuteTrade(ORDER_TYPE_SELL, manipulationHighPrice);
        currentPhase = IDLE; // Reset after trade
    }
    // After a low manipulation, look for price to close back inside the range for a BUY
    else if(currentPhase == MANIPULATION_LOW && currentClose > asianRangeLow)
    {
        ExecuteTrade(ORDER_TYPE_BUY, manipulationLowPrice);
        currentPhase = IDLE; // Reset after trade
    }
}

//+------------------------------------------------------------------+
//| Execute a trade                                                  |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE orderType, double sl_level = 0)
{
    double entryPrice, sl, tp = 0;
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double tp_buffer = InpDynamicTPBufferPips * point * 10;

    if(orderType == ORDER_TYPE_BUY)
    {
        entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        // Improved SL: Place below the actual manipulation low + a buffer
        sl = sl_level - (point * 10 * InpBreakevenBufferPips);

        if(InpUseDynamicTP)
        {
            switch(InpDynamicTPTarget)
            {
                case PREV_DAY_HL:
                    if(prevDayHigh > 0) tp = prevDayHigh - tp_buffer;
                    break;
                case ASIAN_RANGE_OPPOSITE:
                    if(asianRangeHigh > 0) tp = asianRangeHigh - tp_buffer;
                    break;
            }
            // If dynamic target is invalid or below entry, fall back to fixed R:R
            if (tp <= entryPrice)
            {
                tp = entryPrice + (entryPrice - sl) * InpFixedRiskReward;
            }
        }
        else
        {
            tp = entryPrice + (entryPrice - sl) * InpFixedRiskReward;
        }
    }
    else // ORDER_TYPE_SELL
    {
        entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        // Improved SL: Place above the actual manipulation high + a buffer
        sl = sl_level + (point * 10 * InpBreakevenBufferPips);

        if(InpUseDynamicTP)
        {
            switch(InpDynamicTPTarget)
            {
                case PREV_DAY_HL:
                    if(prevDayLow > 0) tp = prevDayLow + tp_buffer;
                    break;
                case ASIAN_RANGE_OPPOSITE:
                    if(asianRangeLow > 0) tp = asianRangeLow + tp_buffer;
                    break;
            }
            // If dynamic target is invalid or above entry, fall back to fixed R:R
            if (tp == 0 || tp >= entryPrice)
            {
                tp = entryPrice - (sl - entryPrice) * InpFixedRiskReward;
            }
        }
        else
        {
            tp = entryPrice - (sl - entryPrice) * InpFixedRiskReward;
        }
    }

    // --- Minimum R:R Check ---
    double risk_dist = MathAbs(entryPrice - sl);
    double reward_dist = MathAbs(tp - entryPrice);
    if (risk_dist > 0 && (reward_dist / risk_dist) < InpMinRiskReward)
    {
        if(InpNotifyOnError) SendNotification("Trade Skipped", StringFormat("Dynamic TP did not meet min R:R of 1:%.1f.", InpMinRiskReward));
        return; // Skip trade
    }

    double lots = CalculatePositionSize(sl);
    if(lots <= 0) 
    {
        if(InpNotifyOnError) SendNotification("Trade Error", "Invalid lot size.");
        return;
    }

    bool result = (orderType == ORDER_TYPE_BUY) ? trade.Buy(lots, _Symbol, entryPrice, sl, tp, InpTradeComment) : trade.Sell(lots, _Symbol, entryPrice, sl, tp, InpTradeComment);

    if(result)
    {
        if(InpNotifyOnTrade) SendNotification("New PO3 Trade", StringFormat("%s @ %.5f, SL: %.5f", EnumToString(orderType), entryPrice, sl));
    }
    else
    {
        if(InpNotifyOnError) SendNotification("Execution Error", StringFormat("Failed. Error %d: %s", trade.ResultRetcode(), trade.ResultComment()));
    }
}

//+------------------------------------------------------------------+
//| Manage Trailing Stops for Open Positions                         |
//+------------------------------------------------------------------+
void ManageTrailingStops()
{
    if(!InpUseTrailingStop) return;

    double atr_buffer[];
    ArraySetAsSeries(atr_buffer, true);
    if(CopyBuffer(atr_handle, 0, 0, 1, atr_buffer) < 1) return;
    double current_atr = atr_buffer[0];

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {   
        // Find the corresponding managed state for this position
        int pos_idx = -1;
        ulong ticket = PositionGetTicket(i); // Get ticket first
        if(PositionGetInteger(POSITION_MAGIC) != InpMagicNumber || PositionGetString(POSITION_SYMBOL) != _Symbol) continue;

        for(int j = 0; j < ArraySize(managedPositions); j++)
        {
            if(managedPositions[j].ticket == ticket)
            {
                pos_idx = j;
                break;
            }
        }

        if(pos_idx != -1)
        {
            // Now we have the state, we can manage the position
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_sl = PositionGetDouble(POSITION_SL);
            ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
            
            // --- Breakeven Logic (Unified for Buy/Sell) ---
            if (InpUseBreakeven && !managedPositions[pos_idx].breakeven_applied)
            {
                double current_price = SymbolInfoDouble(_Symbol, (type == POSITION_TYPE_BUY) ? SYMBOL_BID : SYMBOL_ASK);
                double profit_pips = (type == POSITION_TYPE_BUY) ? (current_price - open_price) / point / 10 : (open_price - current_price) / point / 10;

                if (profit_pips >= InpBreakevenPips)
                {
                    double breakeven_sl = open_price + ( (type == POSITION_TYPE_BUY ? 1 : -1) * InpBreakevenBufferPips * point * 10 );
                    
                    // Check if new SL is more favorable
                    bool should_modify = (type == POSITION_TYPE_BUY && breakeven_sl > current_sl) || (type == POSITION_TYPE_SELL && breakeven_sl < current_sl);

                    if (should_modify)
                    {
                        if(trade.PositionModify(ticket, breakeven_sl, PositionGetDouble(POSITION_TP)))
                        {
                            managedPositions[pos_idx].breakeven_applied = true; // Set the flag!
                            if(InpNotifyOnTrade) SendNotification("Breakeven", StringFormat("Position #%d moved to breakeven.", ticket));
                        }
                    }
                }
            }

            // --- Trailing Stop Logic (Unified for Buy/Sell) ---
            if (InpUseTrailingStop)
            {
                double current_price = SymbolInfoDouble(_Symbol, (type == POSITION_TYPE_BUY) ? SYMBOL_BID : SYMBOL_ASK);
                double profit_pips = (type == POSITION_TYPE_BUY) ? (current_price - open_price) / point / 10 : (open_price - current_price) / point / 10;

                if(profit_pips > InpTrailStartPips)
                {
                    double new_sl = current_price + ( (type == POSITION_TYPE_BUY ? -1 : 1) * current_atr * InpTrailDistanceATR );
                    
                    // Check if new SL is more favorable
                    bool should_modify = (type == POSITION_TYPE_BUY && new_sl > current_sl) || (type == POSITION_TYPE_SELL && new_sl < current_sl);

                    if(should_modify) { trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP)); }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Manage Partial Take Profits                                      |
//+------------------------------------------------------------------+
void ManagePartialTakeProfits()
{
    if(!InpUsePartialTakeProfits) return;

    // Loop through our tracked positions, not all open positions
    for(int i = ArraySize(managedPositions) - 1; i >= 0; i--)
    {
        // Check if the position still exists
        if(!PositionSelectByTicket(managedPositions[i].ticket)) continue;

        double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
        ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double current_price = SymbolInfoDouble(_Symbol, (type == POSITION_TYPE_BUY) ? SYMBOL_BID : SYMBOL_ASK);
        double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

        // Calculate total pips required for the initial TP
        double initial_tp_pips = MathAbs(managedPositions[i].initial_tp - open_price) / point / 10;
        if(initial_tp_pips == 0) continue; // Avoid division by zero

        // Calculate current profit in pips
        double current_profit_pips = 0;
        if (type == POSITION_TYPE_BUY) current_profit_pips = (current_price - open_price) / point / 10;
        else current_profit_pips = (open_price - current_price) / point / 10;

        // --- Partial TP 1 Logic ---
        // Check if not already taken and if profit target is reached
        if(!managedPositions[i].partial1_taken && current_profit_pips >= initial_tp_pips * InpPartialTP1Ratio)
        {
            // Calculate volume to close based on INITIAL volume
            double volume_to_close = managedPositions[i].initial_volume * InpPartialTP1Volume;
            double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);

            if (volume_to_close >= min_lot)
            {
                if(trade.PositionClosePartial(managedPositions[i].ticket, volume_to_close))
                {
                    managedPositions[i].partial1_taken = true; // Mark as taken to prevent re-triggering
                    if(InpNotifyOnTrade) SendNotification("Partial TP 1", StringFormat("Closed %.2f lots for position #%d.", volume_to_close, managedPositions[i].ticket));
                }
            }
        }

        // --- Partial TP 2 Logic ---
        // Check if not already taken and if profit target is reached
        if(!managedPositions[i].partial2_taken && current_profit_pips >= initial_tp_pips * InpPartialTP2Ratio)
        {
            // Calculate volume to close based on INITIAL volume
            double volume_to_close = managedPositions[i].initial_volume * InpPartialTP2Volume;
            double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);

            if (volume_to_close >= min_lot)
            {
                if(trade.PositionClosePartial(managedPositions[i].ticket, volume_to_close))
                {
                    managedPositions[i].partial2_taken = true; // Mark as taken
                    if(InpNotifyOnTrade) SendNotification("Partial TP 2", StringFormat("Closed %.2f lots for position #%d.", volume_to_close, managedPositions[i].ticket));
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Risk Management Checks                                           |
//+------------------------------------------------------------------+
bool IsRiskManaged()
{
    if(dailyLossLimitHit) { static datetime t=0; if(TimeCurrent()-t>3600) { if(InpNotifyOnError) SendNotification("Risk Alert", "Daily loss limit hit."); t=TimeCurrent(); } return false; }
    if(consecutiveLosses >= InpMaxConsecutiveLosses) { if(InpNotifyOnError) SendNotification("Risk Alert", StringFormat("Max consecutive losses (%d) reached.", InpMaxConsecutiveLosses)); return false; }
    double spread_pips = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / (SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 10);
    if(spread_pips > InpMaxSpreadPips) { if(InpNotifyOnError) SendNotification("Risk Alert", StringFormat("Spread (%.1f) is too high.", spread_pips)); return false; }
    return true;
}

//+------------------------------------------------------------------+
//| Calculate Position Size based on risk percentage                 |
//+------------------------------------------------------------------+
double CalculatePositionSize(double stopLoss)
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = balance * (InpRiskPercentage / 100.0);
    double price = (stopLoss < SymbolInfoDouble(_Symbol, SYMBOL_BID)) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl_dist = MathAbs(price - stopLoss);
    if(sl_dist == 0) return 0;

    double lot_size = 0;
    double tick_val = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    if(tick_val > 0 && tick_size > 0) { double risk_per_lot = (sl_dist / tick_size) * tick_val; if(risk_per_lot > 0) lot_size = risk_amount / risk_per_lot; }

    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN), max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX), step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    lot_size = MathRound(lot_size / step) * step;
    if(lot_size < min_lot) lot_size = min_lot;
    if(lot_size > max_lot) lot_size = max_lot;

    return lot_size;
}

//+------------------------------------------------------------------+
//| Handle trade events to track P/L and risk                        |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans, const MqlTradeRequest& request, const MqlTradeResult& result)
{
    if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
    {
        if(!HistoryDealSelect(trans.deal)) return;

        // We only care about deals related to our EA - check magic number from the deal itself for compatibility
        if(HistoryDealGetInteger(trans.deal, DEAL_MAGIC) != InpMagicNumber) return;

        long deal_entry = HistoryDealGetInteger(trans.deal, DEAL_ENTRY);
        ulong position_id = HistoryDealGetInteger(trans.deal, DEAL_POSITION_ID);

        // A new position was opened
        if(deal_entry == DEAL_ENTRY_IN)
        {
            PositionState newPos;
            newPos.ticket = position_id;
            newPos.initial_volume = HistoryDealGetDouble(trans.deal, DEAL_VOLUME);
            newPos.initial_sl = request.sl; // Get SL from the original request
            newPos.initial_tp = request.tp; // Get TP from the original request
            newPos.partial1_taken = false;
            newPos.partial2_taken = false;
            newPos.breakeven_applied = false; // Initialize new flag

            int size = ArraySize(managedPositions);
            ArrayResize(managedPositions, size + 1);
            managedPositions[size] = newPos;
        }
        // A position was fully or partially closed
        else if(deal_entry == DEAL_ENTRY_OUT)
        {
            // Track profit/loss for risk management
            double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
            if(profit < 0) consecutiveLosses++; else consecutiveLosses = 0;

            if(InpNotifyOnTrade) SendNotification("Trade Closed", StringFormat("Position #%d closed. Profit: %.2f", HistoryDealGetInteger(trans.deal, DEAL_POSITION_ID), profit));

            if(!dailyLossLimitHit)
            {
                double daily_loss_pct = (dailyStartBalance - AccountInfoDouble(ACCOUNT_BALANCE)) / dailyStartBalance * 100.0;
                if(daily_loss_pct >= InpMaxDailyLossPercent)
                {
                    dailyLossLimitHit = true;
                    if(InpNotifyOnError) SendNotification("RISK LIMIT HIT", StringFormat("Daily loss limit of %.2f%% reached.", InpMaxDailyLossPercent));
                }
            }

            // If the position is fully closed, remove it from our tracking array
            if(!PositionSelectByTicket(position_id))
            {
                for(int i = ArraySize(managedPositions) - 1; i >= 0; i--)
                {
                    if(managedPositions[i].ticket == position_id)
                    {
                        ArrayRemove(managedPositions, i, 1);
                        break;
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Reset daily counters at the start of a new day                   |
//+------------------------------------------------------------------+
void CheckNewDay()
{
    MqlDateTime dt; TimeToStruct(TimeCurrent(), dt);
    MqlDateTime last_dt; TimeToStruct(lastTradeDay, last_dt);

    if(dt.day_of_year != last_dt.day_of_year || dt.year != last_dt.year)
    {
        UpdateDailyData(false);
    }
}

//+------------------------------------------------------------------+
//| Updates daily data and resets counters                           |
//+------------------------------------------------------------------+
void UpdateDailyData(bool is_init)
{
    dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    dailyLossLimitHit = false;
    consecutiveLosses = 0;
    currentPhase = IDLE;
    lastTradeDay = TimeCurrent();
    
    // Calculate previous day's high and low for dynamic TP
    MqlRates rates[];
    // On init, we get bar[1]. On a new day, we also get bar[1]. This is correct.
    if(CopyRates(_Symbol, PERIOD_D1, 1, 1, rates) > 0)
    {
        prevDayHigh = rates[0].high;
        prevDayLow = rates[0].low;
        Print("Previous Day H/L updated for Dynamic TP: H=%.5f, L=%.5f", prevDayHigh, prevDayLow);
    }
    else
    {
        prevDayHigh = 0;
        prevDayLow = 0;
        Print("Could not get previous day's H/L for dynamic TP.");
    }

    if(!is_init) SendNotification("New Day", "Daily risk limits & PO3 state have been reset.");
}
