//+------------------------------------------------------------------+
//|                        CCI_Smart_Trader_v3.mq5                  |
//|                      Copyright 2025, Smart Trading Systems      |
//|                         Redesigned for Profitability            |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Smart Trading Systems"
#property link      "https://www.smarttrading.com"
#property version   "3.00"

#include <Trade/Trade.mqh>

//--- Input Parameters
//--- Strategy Settings
input group "=== Smart CCI Strategy Settings ===="
input ENUM_TIMEFRAMES InpExecutionTimeframe = PERIOD_M15;   // Execution Timeframe
input ENUM_TIMEFRAMES InpTrendTimeframe = PERIOD_H1;        // Trend Timeframe
input ENUM_TIMEFRAMES InpHigherTimeframe = PERIOD_H4;       // Higher Timeframe for Context
input int             InpCCI_Period = 20;                  // CCI Period (Increased)
input int             InpATR_Period = 14;                  // ATR Period
input int             InpEMA_Fast = 21;                    // Fast EMA for Trend
input int             InpEMA_Slow = 50;                    // Slow EMA for Trend
input int             InpRSI_Period = 14;                  // RSI for Additional Filter
input int             InpSwingLookback = 20;               // Swing High/Low Lookback

//--- Smart Filters
input group "=== Smart Filters ===="
input bool            InpUseMultiTimeframe = true;         // Enable Multi-Timeframe Analysis
input bool            InpUseVolumeFilter = true;           // Require Volume Confirmation
input double          InpMinVolumeMultiplier = 1.3;        // Min Volume vs Average
input int             InpVolumeLookback = 10;              // Volume Average Lookback
input bool            InpUseTrendStrength = true;          // Require Strong Trend
input double          InpMinTrendStrength = 0.7;           // Min Trend Strength (0-1)
input bool            InpUseRSIFilter = true;              // Use RSI Divergence Filter
input double          InpCCI_EntryLevel = 150;             // CCI Entry Level (vs 100)
input double          InpCCI_ExitLevel = 50;               // CCI Exit Level (vs 0)
input double          InpFilterConfirmationPercent = 70.0; // Min % of filters that must agree

//--- Enhanced Risk Management
input group "=== Enhanced Risk Management ===="
input double          InpRiskPercentage = 0.5;             // Risk per trade (Reduced)
input double          InpMaxDailyLoss = 2.0;               // Max daily loss (Reduced)
input int             InpMaxConcurrentTrades = 1;          // Max concurrent trades
input double          InpMaxDrawdownPercent = 10.0;        // Max drawdown before pause
input int             InpMaxConsecutiveLosses = 3;         // Max consecutive losses
input double          InpMinRiskReward = 2.0;              // Minimum R:R ratio
input bool            InpUseAdaptiveRisk = true;           // Adaptive risk after losses
input double          InpMaxPositionSize = 5.0;            // Max position size (% of account)

//--- Smart Exit Settings
input group "=== Smart Exit Settings ===="
input bool            InpUseSmartTP = true;                // Use Smart Take Profit
input double          InpBaseRiskReward = 2.5;             // Base Risk:Reward
input double          InpMaxRiskReward = 5.0;              // Max Risk:Reward
input bool            InpUseBreakeven = true;              // Enable Breakeven
input double          InpBreakevenRR = 0.8;                // R:R to move to breakeven
input double          InpBreakevenBuffer = 0.2;            // Breakeven buffer R:R
input bool            InpUseTrailingStop = true;           // Enable Smart Trailing
input double          InpTrailStartRR = 1.5;               // R:R to start trailing
input double          InpTrailStep = 0.5;                  // Trailing step R:R

//--- Session Management
input group "=== Session Management ===="
input bool            InpTradeLondonSession = true;        // Trade London Session
input bool            InpTradeNYSession = true;            // Trade NY Session
input bool            InpTradeAsianSession = false;        // Trade Asian Session
input int             InpMaxTradesPerSession = 3;          // Max trades per session
input int             InpMinTimeBetweenTrades = 60;        // Min minutes between trades

//--- Notification Settings
input group "=== Notification Settings ===="
input bool   InpEnableAlerts = true;                      // Enable Alerts
input bool   InpEnablePushNotifications = false;          // Enable Push Notifications
input bool   InpNotifyOnOpen = true;                      // Notify on Trade Open
input bool   InpNotifyOnClose = true;                     // Notify on Trade Close
input bool   InpNotifyOnRisk = true;                      // Notify on Risk Events

//--- General Settings
input group "=== General Settings ===="
input ulong           InpMagicNumber = 987654;            // Magic Number
input string          InpTradeComment = "CCI_Smart_v3";   // Trade Comment

//--- Global Variables
//--- Indicator Handles
int h_cci_exec, h_cci_trend, h_cci_higher;
int h_atr_exec;
int h_ema_fast_trend, h_ema_slow_trend;
int h_ema_fast_higher, h_ema_slow_higher;
int h_rsi_exec;

//--- Trading Object
CTrade trade;

//--- Risk Management Variables
double dailyStartBalance, peakEquity;
bool   dailyLossLimitReached = false;
bool   drawdownLimitReached = false;
int    consecutiveLosses = 0;
datetime lastTradeTime = 0;
datetime lastDayCheck = 0;

//--- Session Variables
int londonTrades = 0, nyTrades = 0, asianTrades = 0;

//--- Position Management
struct PositionState
{
   ulong   ticket;
   double  entryPrice;
   double  stopLoss;
   double  riskAmount;
   bool    breakevenApplied;
   bool    trailingActive;
};
PositionState managedPositions[];

//+------------------------------------------------------------------+
//| Send Notifications                                               |
//+------------------------------------------------------------------+
void SendNotification(string subject, string message)
{
    if(IsStopped()) return;
    
    if(InpEnableAlerts) Alert(subject, "\n", message);
    if(InpEnablePushNotifications)
    {
        string combined = subject + ": " + message;
        if(StringLen(combined) > 255) combined = StringSubstr(combined, 0, 255);
        SendNotification(combined);
    }
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetTypeFillingBySymbol(_Symbol);

    // Initialize indicators
    h_cci_exec = iCCI(_Symbol, InpExecutionTimeframe, InpCCI_Period, PRICE_TYPICAL);
    h_cci_trend = iCCI(_Symbol, InpTrendTimeframe, InpCCI_Period, PRICE_TYPICAL);
    h_cci_higher = iCCI(_Symbol, InpHigherTimeframe, InpCCI_Period, PRICE_TYPICAL);
    
    h_atr_exec = iATR(_Symbol, InpExecutionTimeframe, InpATR_Period);
    
    h_ema_fast_trend = iMA(_Symbol, InpTrendTimeframe, InpEMA_Fast, 0, MODE_EMA, PRICE_CLOSE);
    h_ema_slow_trend = iMA(_Symbol, InpTrendTimeframe, InpEMA_Slow, 0, MODE_EMA, PRICE_CLOSE);
    h_ema_fast_higher = iMA(_Symbol, InpHigherTimeframe, InpEMA_Fast, 0, MODE_EMA, PRICE_CLOSE);
    h_ema_slow_higher = iMA(_Symbol, InpHigherTimeframe, InpEMA_Slow, 0, MODE_EMA, PRICE_CLOSE);
    
    h_rsi_exec = iRSI(_Symbol, InpExecutionTimeframe, InpRSI_Period, PRICE_CLOSE);
    // Note: Volume will be accessed directly via CopyTickVolume, no handle needed

    // Check handles
    if(h_cci_exec == INVALID_HANDLE || h_atr_exec == INVALID_HANDLE || 
       h_ema_fast_trend == INVALID_HANDLE || h_rsi_exec == INVALID_HANDLE)
    {
        Print("Error creating indicators");
        return INIT_FAILED;
    }

    // Initialize risk management
    dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    peakEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    
    Print("=== CCI Smart Trader v3.0 Initialized ===");
    Print("Risk per trade: ", InpRiskPercentage, "%");
    Print("Max daily loss: ", InpMaxDailyLoss, "%");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    IndicatorRelease(h_cci_exec);
    IndicatorRelease(h_cci_trend);
    IndicatorRelease(h_cci_higher);
    IndicatorRelease(h_atr_exec);
    IndicatorRelease(h_ema_fast_trend);
    IndicatorRelease(h_ema_slow_trend);
    IndicatorRelease(h_ema_fast_higher);
    IndicatorRelease(h_ema_slow_higher);
    IndicatorRelease(h_rsi_exec);
    
    Print("CCI Smart Trader v3.0 Deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    ManagePositions();

    static datetime lastBarTime = 0;
    datetime newBarTime = (datetime)SeriesInfoInteger(_Symbol, InpExecutionTimeframe, SERIES_LASTBAR_DATE);

    if(newBarTime > lastBarTime)
    {
        lastBarTime = newBarTime;
        
        CheckNewDay();
        CheckForNewTrade();
    }
}

//+------------------------------------------------------------------+
//| Check for new trade opportunities with 70% filter confirmation  |
//+------------------------------------------------------------------+
void CheckForNewTrade()
{
    if(!IsRiskManaged()) return;
    if(!IsSessionActive()) return;
    if(!IsTimeToTrade()) return;

    // Get multi-timeframe analysis
    int execTrend = GetCCITrend(h_cci_exec);
    if(execTrend == 0) return;

    // Calculate filter confirmation score
    int totalFilters = 0;
    int passedFilters = 0;
    
    // Filter 1: Multi-timeframe alignment
    if(InpUseMultiTimeframe)
    {
        totalFilters++;
        int trendTrend = GetCCITrend(h_cci_trend);
        int higherTrend = GetCCITrend(h_cci_higher);
        if(execTrend == trendTrend && trendTrend == higherTrend)
            passedFilters++;
    }
    
    // Filter 2: EMA trend alignment
    totalFilters++;
    if(IsEMATrendAligned(execTrend))
        passedFilters++;
    
    // Filter 3: Volume confirmation
    if(InpUseVolumeFilter)
    {
        totalFilters++;
        if(IsVolumeConfirmed())
            passedFilters++;
    }
    
    // Filter 4: RSI filter
    if(InpUseRSIFilter)
    {
        totalFilters++;
        if(IsRSIFavorable(execTrend))
            passedFilters++;
    }
    
    // Filter 5: Trend strength
    if(InpUseTrendStrength)
    {
        totalFilters++;
        if(IsTrendStrong())
            passedFilters++;
    }
    
    // Filter 6: Smart CCI entry (always required)
    totalFilters++;
    if(IsSmartCCIEntry(execTrend))
        passedFilters++;
    else
        return; // CCI entry signal is mandatory
    
    // Calculate confirmation percentage
    double confirmationPercent = (double)passedFilters / totalFilters * 100.0;
    
    // Check if enough filters confirm
    if(confirmationPercent >= InpFilterConfirmationPercent)
    {
        ENUM_ORDER_TYPE orderType = (execTrend == 1) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
        ExecuteSmartTrade(orderType);
    }
}

//+------------------------------------------------------------------+
//| Get CCI trend direction with smart levels                        |
//+------------------------------------------------------------------+
int GetCCITrend(int handle)
{
    double cci[];
    ArraySetAsSeries(cci, true);
    if(CopyBuffer(handle, 0, 0, 3, cci) < 3) return 0;
    
    // Smart entry: Look for CCI crossing back from extreme levels
    if(cci[2] <= -InpCCI_EntryLevel && cci[1] > -InpCCI_EntryLevel && cci[0] > cci[1])
        return 1; // Bullish signal
        
    if(cci[2] >= InpCCI_EntryLevel && cci[1] < InpCCI_EntryLevel && cci[0] < cci[1])
        return -1; // Bearish signal
    
    return 0;
}

//+------------------------------------------------------------------+
//| Check EMA trend alignment                                        |
//+------------------------------------------------------------------+
bool IsEMATrendAligned(int direction)
{
    double ema_fast[1], ema_slow[1];
    
    if(CopyBuffer(h_ema_fast_trend, 0, 0, 1, ema_fast) < 1) return false;
    if(CopyBuffer(h_ema_slow_trend, 0, 0, 1, ema_slow) < 1) return false;
    
    bool bullish = ema_fast[0] > ema_slow[0];
    
    return (direction == 1 && bullish) || (direction == -1 && !bullish);
}

//+------------------------------------------------------------------+
//| Check volume confirmation                                        |
//+------------------------------------------------------------------+
bool IsVolumeConfirmed()
{
    long volumes[];
    ArraySetAsSeries(volumes, true);
    if(CopyTickVolume(_Symbol, InpExecutionTimeframe, 0, InpVolumeLookback + 1, volumes) < InpVolumeLookback + 1)
        return false;
    
    long avgVolume = 0;
    for(int i = 1; i <= InpVolumeLookback; i++)
        avgVolume += volumes[i];
    avgVolume /= InpVolumeLookback;
    
    return volumes[0] >= (avgVolume * InpMinVolumeMultiplier);
}

//+------------------------------------------------------------------+
//| Check RSI for favorable conditions                              |
//+------------------------------------------------------------------+
bool IsRSIFavorable(int direction)
{
    double rsi[1];
    if(CopyBuffer(h_rsi_exec, 0, 0, 1, rsi) < 1) return false;
    
    if(direction == 1) return rsi[0] < 70;  // Not overbought for buy
    if(direction == -1) return rsi[0] > 30; // Not oversold for sell
    
    return false;
}

//+------------------------------------------------------------------+
//| Check trend strength                                             |
//+------------------------------------------------------------------+
bool IsTrendStrong()
{
    double ema_fast[1], ema_slow[1];
    
    if(CopyBuffer(h_ema_fast_higher, 0, 0, 1, ema_fast) < 1) return false;
    if(CopyBuffer(h_ema_slow_higher, 0, 0, 1, ema_slow) < 1) return false;
    
    double price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double ema_diff = MathAbs(ema_fast[0] - ema_slow[0]);
    double price_range = price * 0.01; // 1% of price
    
    double strength = ema_diff / price_range;
    
    return strength >= InpMinTrendStrength;
}

//+------------------------------------------------------------------+
//| Smart CCI entry signal                                          |
//+------------------------------------------------------------------+
bool IsSmartCCIEntry(int direction)
{
    double cci[1];
    if(CopyBuffer(h_cci_exec, 0, 0, 1, cci) < 1) return false;
    
    if(direction == 1) return cci[0] > -InpCCI_ExitLevel && cci[0] < InpCCI_ExitLevel;
    if(direction == -1) return cci[0] < InpCCI_ExitLevel && cci[0] > -InpCCI_ExitLevel;
    
    return false;
}

//+------------------------------------------------------------------+
//| Execute smart trade with enhanced risk management               |
//+------------------------------------------------------------------+
void ExecuteSmartTrade(ENUM_ORDER_TYPE orderType)
{
    double price = (orderType == ORDER_TYPE_BUY) ? 
        SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    double sl = CalculateSmartStopLoss(orderType, price);
    if(sl == 0) return;
    
    double tp = CalculateSmartTakeProfit(orderType, price, sl);
    if(tp == 0) return;
    
    // Risk-reward validation
    double risk = MathAbs(price - sl);
    double reward = MathAbs(tp - price);
    double rr = reward / risk;
    
    if(rr < InpMinRiskReward)
    {
        if(InpNotifyOnRisk) SendNotification("Trade Skipped", 
            StringFormat("R:R %.2f below minimum %.2f", rr, InpMinRiskReward));
        return;
    }
    
    double lots = CalculateSmartPositionSize(sl, price);
    if(lots <= 0) return;
    
    bool result = false;
    if(orderType == ORDER_TYPE_BUY)
        result = trade.Buy(lots, _Symbol, price, sl, tp, InpTradeComment);
    else
        result = trade.Sell(lots, _Symbol, price, sl, tp, InpTradeComment);
    
    if(result)
    {
        lastTradeTime = TimeCurrent();
        UpdateSessionCount();
        
        if(InpNotifyOnOpen)
        {
            SendNotification("Smart Trade Opened", 
                StringFormat("%s %.2f lots @ %.5f, SL: %.5f, TP: %.5f, R:R: %.2f",
                EnumToString(orderType), lots, price, sl, tp, rr));
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate smart stop loss                                       |
//+------------------------------------------------------------------+
double CalculateSmartStopLoss(ENUM_ORDER_TYPE orderType, double price)
{
    // Use swing high/low method
    MqlRates rates[];
    if(CopyRates(_Symbol, InpExecutionTimeframe, 1, InpSwingLookback, rates) < InpSwingLookback)
        return 0;
    
    double atr[1];
    if(CopyBuffer(h_atr_exec, 0, 0, 1, atr) < 1) return 0;
    
    double buffer = atr[0] * 0.5;
    double sl = 0;
    
    if(orderType == ORDER_TYPE_BUY)
    {
        double lowestLow = rates[0].low;
        for(int i = 1; i < InpSwingLookback; i++)
            if(rates[i].low < lowestLow) lowestLow = rates[i].low;
        sl = lowestLow - buffer;
    }
    else
    {
        double highestHigh = rates[0].high;
        for(int i = 1; i < InpSwingLookback; i++)
            if(rates[i].high > highestHigh) highestHigh = rates[i].high;
        sl = highestHigh + buffer;
    }
    
    return NormalizeDouble(sl, _Digits);
}

//+------------------------------------------------------------------+
//| Calculate smart take profit                                     |
//+------------------------------------------------------------------+
double CalculateSmartTakeProfit(ENUM_ORDER_TYPE orderType, double price, double sl)
{
    double risk = MathAbs(price - sl);
    double baseReward = risk * InpBaseRiskReward;
    
    // Look for nearby resistance/support
    double smartTP = 0;
    if(orderType == ORDER_TYPE_BUY)
        smartTP = price + baseReward;
    else
        smartTP = price - baseReward;
    
    // Cap at maximum R:R
    double maxReward = risk * InpMaxRiskReward;
    if(orderType == ORDER_TYPE_BUY && smartTP > price + maxReward)
        smartTP = price + maxReward;
    else if(orderType == ORDER_TYPE_SELL && smartTP < price - maxReward)
        smartTP = price - maxReward;
    
    return NormalizeDouble(smartTP, _Digits);
}

//+------------------------------------------------------------------+
//| Calculate smart position size with enhanced risk management     |
//+------------------------------------------------------------------+
double CalculateSmartPositionSize(double sl, double price)
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskPct = GetAdaptiveRisk();
    double riskAmount = balance * (riskPct / 100.0);
    
    double slDistance = MathAbs(price - sl);
    if(slDistance == 0) return 0;
    
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    double lots = 0;
    if(tickValue > 0 && tickSize > 0)
    {
        double riskPerLot = (slDistance / tickSize) * tickValue;
        if(riskPerLot > 0) lots = riskAmount / riskPerLot;
    }
    
    // Apply position size limit
    double maxPositionValue = balance * (InpMaxPositionSize / 100.0);
    double contractSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_CONTRACT_SIZE);
    double maxLots = maxPositionValue / (price * contractSize);
    
    if(lots > maxLots) lots = maxLots;
    
    // Normalize to lot step
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lots = MathRound(lots / lotStep) * lotStep;
    
    if(lots < minLot) lots = minLot;
    if(lots > maxLot) lots = maxLot;
    
    return lots;
}

//+------------------------------------------------------------------+
//| Get adaptive risk based on consecutive losses                   |
//+------------------------------------------------------------------+
double GetAdaptiveRisk()
{
    if(!InpUseAdaptiveRisk) return InpRiskPercentage;
    
    double risk = InpRiskPercentage;
    if(consecutiveLosses >= 1) risk *= 0.75;  // 25% reduction after 1 loss
    if(consecutiveLosses >= 2) risk *= 0.5;   // 50% reduction after 2 losses
    if(consecutiveLosses >= 3) risk *= 0.25;  // 75% reduction after 3 losses
    
    return MathMax(risk, 0.1); // Minimum 0.1%
}

//+------------------------------------------------------------------+
//| Enhanced position management                                     |
//+------------------------------------------------------------------+
void ManagePositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetInteger(POSITION_MAGIC) != InpMagicNumber) continue;
        if(PositionGetString(POSITION_SYMBOL) != _Symbol) continue;
        
        ManageSmartExits(ticket);
    }
}

//+------------------------------------------------------------------+
//| Smart exit management                                           |
//+------------------------------------------------------------------+
void ManageSmartExits(ulong ticket)
{
    if(!PositionSelectByTicket(ticket)) return;
    
    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    double currentSL = PositionGetDouble(POSITION_SL);
    double currentTP = PositionGetDouble(POSITION_TP);
    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    
    double currentPrice = (posType == POSITION_TYPE_BUY) ?
        SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    double risk = MathAbs(openPrice - currentSL);
    double currentProfit = (posType == POSITION_TYPE_BUY) ?
        (currentPrice - openPrice) : (openPrice - currentPrice);
    
    double currentRR = (risk > 0) ? currentProfit / risk : 0;
    
    // Find position state
    int posIndex = -1;
    for(int i = 0; i < ArraySize(managedPositions); i++)
    {
        if(managedPositions[i].ticket == ticket)
        {
            posIndex = i;
            break;
        }
    }
    
    // Breakeven management
    if(InpUseBreakeven && posIndex >= 0 && !managedPositions[posIndex].breakevenApplied && 
       currentRR >= InpBreakevenRR)
    {
        double newSL = openPrice + ((posType == POSITION_TYPE_BUY ? 1 : -1) * 
            InpBreakevenBuffer * risk);
        
        if(trade.PositionModify(ticket, newSL, currentTP))
        {
            managedPositions[posIndex].breakevenApplied = true;
            if(InpNotifyOnClose) SendNotification("Breakeven Applied", 
                StringFormat("Position #%d moved to breakeven", ticket));
        }
    }
    
    // Trailing stop management
    if(InpUseTrailingStop && currentRR >= InpTrailStartRR)
    {
        double trailDistance = InpTrailStep * risk;
        double newSL = currentPrice - ((posType == POSITION_TYPE_BUY ? 1 : -1) * trailDistance);
        
        bool shouldModify = (posType == POSITION_TYPE_BUY && newSL > currentSL) ||
                           (posType == POSITION_TYPE_SELL && newSL < currentSL);
        
        if(shouldModify)
        {
            trade.PositionModify(ticket, NormalizeDouble(newSL, _Digits), currentTP);
        }
    }
    
    // Smart CCI exit
    if(InpUseSmartTP)
    {
        double cci[1];
        if(CopyBuffer(h_cci_exec, 0, 0, 1, cci) < 1) return;
        
        bool exitSignal = false;
        if(posType == POSITION_TYPE_BUY && cci[0] <= -InpCCI_ExitLevel)
            exitSignal = true;
        else if(posType == POSITION_TYPE_SELL && cci[0] >= InpCCI_ExitLevel)
            exitSignal = true;
        
        if(exitSignal && currentRR >= 1.0) // Only exit if at least 1:1
        {
            trade.PositionClose(ticket);
            if(InpNotifyOnClose) SendNotification("Smart Exit", 
                StringFormat("Position #%d closed by CCI signal at %.2f R:R", ticket, currentRR));
        }
    }
}

//+------------------------------------------------------------------+
//| Risk management checks                                           |
//+------------------------------------------------------------------+
bool IsRiskManaged()
{
    if(dailyLossLimitReached || drawdownLimitReached) return false;
    if(PositionsTotal() >= InpMaxConcurrentTrades) return false;
    
    // Check drawdown
    double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    if(currentEquity > peakEquity) peakEquity = currentEquity;
    
    double drawdown = (peakEquity - currentEquity) / peakEquity * 100.0;
    if(drawdown >= InpMaxDrawdownPercent)
    {
        drawdownLimitReached = true;
        if(InpNotifyOnRisk) SendNotification("Drawdown Limit", 
            StringFormat("Max drawdown %.1f%% reached", drawdown));
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if current session is active                              |
//+------------------------------------------------------------------+
bool IsSessionActive()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int hour = dt.hour;
    
    // London: 8-17 GMT, NY: 13-22 GMT, Asian: 23-8 GMT
    bool london = InpTradeLondonSession && (hour >= 8 && hour < 17);
    bool ny = InpTradeNYSession && (hour >= 13 && hour < 22);
    bool asian = InpTradeAsianSession && (hour >= 23 || hour < 8);
    
    return london || ny || asian;
}

//+------------------------------------------------------------------+
//| Check if enough time has passed since last trade               |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
    if(lastTradeTime == 0) return true;
    return (TimeCurrent() - lastTradeTime) >= (InpMinTimeBetweenTrades * 60);
}

//+------------------------------------------------------------------+
//| Update session trade count                                      |
//+------------------------------------------------------------------+
void UpdateSessionCount()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int hour = dt.hour;
    
    if(hour >= 8 && hour < 17) londonTrades++;
    else if(hour >= 13 && hour < 22) nyTrades++;
    else asianTrades++;
}

//+------------------------------------------------------------------+
//| Handle trade transactions                                       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                        const MqlTradeRequest& request,
                        const MqlTradeResult& result)
{
    if(trans.type != TRADE_TRANSACTION_DEAL_ADD) return;
    if(!HistoryDealSelect(trans.deal)) return;
    if(HistoryDealGetInteger(trans.deal, DEAL_MAGIC) != InpMagicNumber) return;

    long dealEntry = HistoryDealGetInteger(trans.deal, DEAL_ENTRY);
    ulong positionId = HistoryDealGetInteger(trans.deal, DEAL_POSITION_ID);

    if(dealEntry == DEAL_ENTRY_IN)
    {
        // New position opened
        PositionState newPos;
        newPos.ticket = positionId;
        newPos.entryPrice = HistoryDealGetDouble(trans.deal, DEAL_PRICE);
        newPos.breakevenApplied = false;
        newPos.trailingActive = false;

        int size = ArraySize(managedPositions);
        ArrayResize(managedPositions, size + 1);
        managedPositions[size] = newPos;
    }
    else if(dealEntry == DEAL_ENTRY_OUT)
    {
        // Position closed
        if(!PositionSelectByTicket(positionId))
        {
            double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
            
            if(profit < 0) consecutiveLosses++;
            else consecutiveLosses = 0;
            
            if(InpNotifyOnClose)
            {
                SendNotification("Trade Closed", 
                    StringFormat("Position #%d closed with profit: %.2f", positionId, profit));
            }
            
            // Check daily loss limit
            CheckDailyLoss();
            
            // Remove from managed positions
            for(int i = ArraySize(managedPositions) - 1; i >= 0; i--)
            {
                if(managedPositions[i].ticket == positionId)
                {
                    ArrayRemove(managedPositions, i, 1);
                    break;
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check daily loss limit                                          |
//+------------------------------------------------------------------+
void CheckDailyLoss()
{
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double dailyLoss = (dailyStartBalance - currentBalance) / dailyStartBalance * 100.0;
    
    if(dailyLoss >= InpMaxDailyLoss)
    {
        dailyLossLimitReached = true;
        if(InpNotifyOnRisk) SendNotification("Daily Loss Limit", 
            StringFormat("Daily loss limit %.1f%% reached", InpMaxDailyLoss));
    }
}

//+------------------------------------------------------------------+
//| Reset daily counters                                            |
//+------------------------------------------------------------------+
void CheckNewDay()
{
    datetime currentDay = iTime(_Symbol, PERIOD_D1, 0);
    if(currentDay > lastDayCheck)
    {
        dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        dailyLossLimitReached = false;
        drawdownLimitReached = false;
        consecutiveLosses = 0;
        londonTrades = 0;
        nyTrades = 0;
        asianTrades = 0;
        lastDayCheck = currentDay;
        
        Print("New trading day - limits reset");
    }
}