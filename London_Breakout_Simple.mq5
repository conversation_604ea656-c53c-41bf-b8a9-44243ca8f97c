//+------------------------------------------------------------------+
//|                          London_Breakout_Simple.mq5            |
//|                     Simple Version for Optimization            |
//+------------------------------------------------------------------+
#property copyright "Simple Version"
#property version   "1.00"

//--- Input Parameters (All Optimizable)
input group "=== London Breakout Settings ===="
input int    InpLondonStartHour = 6;     // London session start (GMT)
input int    InpLondonEndHour = 9;       // London session end (GMT)
input double InpBreakoutBuffer = 3.0;    // Breakout buffer (pips)
input double InpMinRiskReward = 1.8;     // Minimum R:R ratio
input double InpFixedSLPips = 20.0;      // Fixed stop loss (pips)
input int    InpLookbackBars = 16;       // Bars for high/low calculation

input group "=== Risk Settings ===="
input double InpRiskPercentage = 1.0;    // Risk per trade (%)
input ulong  InpMagicNumber = 888999;    // Magic number

//--- Global Variables
bool tradingAllowed = true;
datetime lastTradeDay = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== London Breakout Simple Started ===");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    static datetime lastBarTime = 0;
    datetime newBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(newBarTime > lastBarTime)
    {
        lastBarTime = newBarTime;
        
        MqlDateTime dt;
        TimeToStruct(TimeCurrent(), dt);
        int currentHour = dt.hour;
        
        // Reset trading permission daily
        if(lastTradeDay != iTime(_Symbol, PERIOD_D1, 0))
        {
            lastTradeDay = iTime(_Symbol, PERIOD_D1, 0);
            tradingAllowed = true;
            Print("NEW DAY - Trading reset, Hour: ", currentHour);
        }
        
        // London breakout window
        if(currentHour >= InpLondonStartHour && currentHour < InpLondonEndHour && tradingAllowed)
        {
            CheckBreakout();
        }
    }
}

//+------------------------------------------------------------------+
//| Check for breakout                                               |
//+------------------------------------------------------------------+
void CheckBreakout()
{
    if(!tradingAllowed) return;
    
    // Get recent high/low using lookback period
    double recentHigh = iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, InpLookbackBars, 1));
    double recentLow = iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, InpLookbackBars, 1));
    
    double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 0);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double buffer = InpBreakoutBuffer * point * 10;
    
    bool bullishBreakout = currentPrice > (recentHigh + buffer);
    bool bearishBreakout = currentPrice < (recentLow - buffer);
    
    if(bullishBreakout)
    {
        Print("BULLISH BREAKOUT at ", currentPrice, " (High: ", recentHigh, ")");
        ExecuteTrade(ORDER_TYPE_BUY, currentPrice);
        tradingAllowed = false;
    }
    else if(bearishBreakout)
    {
        Print("BEARISH BREAKOUT at ", currentPrice, " (Low: ", recentLow, ")");
        ExecuteTrade(ORDER_TYPE_SELL, currentPrice);
        tradingAllowed = false;
    }
}

//+------------------------------------------------------------------+
//| Execute trade                                                    |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE orderType, double price)
{
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double slDistance = InpFixedSLPips * point * 10;
    
    // Calculate SL and TP
    double sl = (orderType == ORDER_TYPE_BUY) ? price - slDistance : price + slDistance;
    double risk = MathAbs(price - sl);
    double reward = risk * InpMinRiskReward;
    double tp = (orderType == ORDER_TYPE_BUY) ? price + reward : price - reward;
    
    // Fixed lot size for testing
    double lots = 0.01;
    
    // Execute trade
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lots;
    request.type = orderType;
    request.price = price;
    request.sl = NormalizeDouble(sl, _Digits);
    request.tp = NormalizeDouble(tp, _Digits);
    request.magic = InpMagicNumber;
    request.comment = "LB_Simple";
    
    if(OrderSend(request, result))
    {
        Print("TRADE EXECUTED: ", EnumToString(orderType), " at ", price, " SL:", sl, " TP:", tp);
    }
    else
    {
        Print("TRADE FAILED: Error ", result.retcode);
    }
}